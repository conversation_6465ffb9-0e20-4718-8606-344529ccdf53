<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência - Sistema de Gestão de Certificados</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #1a365d;
            --secondary-color: #2d3748;
            --accent-color: #3182ce;
            --success-color: #38a169;
            --warning-color: #d69e2e;
            --danger-color: #e53e3e;
            --light-color: #f7fafc;
            --white-color: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --shadow-soft: 0 10px 25px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 20px 40px rgba(0, 0, 0, 0.15);
            --border-radius: 12px;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>');
            animation: float 20s ease-in-out infinite;
            z-index: 1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .login-container {
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 0;
            box-shadow: var(--shadow-medium);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            min-height: 600px;
            display: flex;
        }

        .login-left {
            flex: 1;
            background: var(--gradient-primary);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .login-left::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: pulse 4s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.1); }
        }

        .logo-section {
            position: relative;
            z-index: 3;
        }

        .logo-icon {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .logo-icon i {
            font-size: 3rem;
            color: white;
        }

        .brand-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ffffff, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .brand-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
            line-height: 1.6;
        }

        .login-right {
            flex: 1;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }

        .login-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .login-title {
            font-size: 2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .login-description {
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 400;
        }

        .module-tabs {
            display: flex;
            background: var(--light-color);
            border-radius: var(--border-radius);
            padding: 0.5rem;
            margin-bottom: 2rem;
            position: relative;
        }

        .module-tab {
            flex: 1;
            padding: 1rem;
            text-align: center;
            border-radius: calc(var(--border-radius) - 4px);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            color: var(--text-secondary);
            position: relative;
            z-index: 2;
        }

        .module-tab.active {
            color: white;
            background: var(--gradient-primary);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            transform: translateY(-2px);
        }

        .module-tab i {
            display: block;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .form-container {
            position: relative;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: var(--border-radius);
            padding: 1rem 1rem 1rem 3rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
        }

        .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
            outline: none;
            transform: translateY(-1px);
        }

        .form-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            z-index: 3;
            margin-top: 12px;
        }

        .btn-primary {
            background: var(--gradient-primary);
            border: none;
            border-radius: var(--border-radius);
            padding: 1rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            width: 100%;
            margin-top: 1rem;
        }

        .btn-primary.loading {
            cursor: not-allowed;
        }

        .btn-primary.loading .btn-text {
            opacity: 0;
        }

        .btn-primary .spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.5);
            border-top-color: white;
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
            display: none;
        }

        .btn-primary.loading .spinner {
            display: block;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .forgot-password {
            text-align: center;
            margin-top: 1.5rem;
        }

        .forgot-password a {
            color: var(--accent-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .forgot-password a:hover {
            color: var(--primary-color);
        }

        .features-list {
            list-style: none;
            margin-top: 2rem;
        }

        .features-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            opacity: 0.9;
        }

        .features-list li i {
            margin-right: 1rem;
            color: rgba(255, 255, 255, 0.8);
        }

        @media (max-width: 768px) {
            .login-card {
                flex-direction: column;
                margin: 1rem;
            }

            .login-left {
                padding: 2rem;
                min-height: 300px;
            }

            .login-right {
                padding: 2rem;
            }

            .brand-title {
                font-size: 2rem;
            }

            .module-tabs {
                flex-direction: column;
                gap: 0.5rem;
            }

            .module-tab {
                padding: 0.75rem;
            }

            .module-tab i {
                font-size: 1.2rem;
            }
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            backdrop-filter: blur(5px);
        }

        .loading-content {
            text-align: center;
            padding: 2rem;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid var(--accent-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .form-validation {
            font-size: 0.875rem;
            color: var(--danger-color);
            margin-top: 0.5rem;
            display: none;
        }

        .form-control.is-invalid {
            border-color: var(--danger-color);
            box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
        }

        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--text-secondary);
            z-index: 3;
            margin-top: 12px;
        }

        .alert {
            border-radius: var(--border-radius);
            border: none;
            padding: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .alert-info {
            background: linear-gradient(135deg, #ebf8ff, #bee3f8);
            color: #2c5282;
        }

        .alert-danger {
            background: linear-gradient(135deg, #fff5f5, #fed7d7);
            color: #9b2c2c;
        }

        .alert-success {
            background: linear-gradient(135deg, #f0fff4, #c6f6d5);
            color: #2f855a;
        }
    </style>
</head>
<body>
    <!-- Loading Overlay is removed from here, login button will have its own spinner -->

    <div class="login-container">
        <div class="login-card">
            <!-- Left Side - Branding -->
            <div class="login-left">
                <div class="logo-section">
                    <div class="logo-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h1 class="brand-title">Faciência</h1>
                    <p class="brand-subtitle">
                        Sistema completo de gestão de certificados educacionais
                    </p>
                    
                    <ul class="features-list">
                        <li>
                            <i class="fas fa-check-circle"></i>
                            Gestão completa de parceiros
                        </li>
                        <li>
                            <i class="fas fa-check-circle"></i>
                            Controle financeiro integrado
                        </li>
                        <li>
                            <i class="fas fa-check-circle"></i>
                            Emissão automatizada de certificados
                        </li>
                        <li>
                            <i class="fas fa-check-circle"></i>
                            Validação por QR Code
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="login-right">
                <div id="globalAlert" class="position-absolute top-0 start-50 translate-middle-x w-100 p-3" style="z-index: 10;"></div>

                <div class="login-header">
                    <h2 class="login-title">Bem-vindo de volta</h2>
                    <p class="login-description">Selecione seu módulo de acesso e faça login</p>
                </div>

                <!-- Module Selection Tabs -->
                <div class="module-tabs">
                    <div class="module-tab active" data-module="admin">
                        <i class="fas fa-user-shield"></i>
                        <span>Administrador</span>
                    </div>
                    <div class="module-tab" data-module="financial">
                        <i class="fas fa-chart-line"></i>
                        <span>Financeiro</span>
                    </div>
                    <div class="module-tab" data-module="partner">
                        <i class="fas fa-users"></i>
                        <span>Parceiro</span>
                    </div>
                </div>

                <!-- Login Form -->
                <div class="form-container">
                    <form id="loginForm">
                        <!-- Admin Module Info -->
                        <div id="admin-info" class="alert alert-info">
                            <strong>Módulo Administrador:</strong> Acesso completo para gestão de parceiros, contratos e relatórios do sistema.
                        </div>

                        <!-- Financial Module Info -->
                        <div id="financial-info" class="alert alert-info" style="display: none;">
                            <strong>Módulo Financeiro:</strong> Controle de recebimentos, emissão de boletos e acompanhamento de pagamentos.
                        </div>

                        <!-- Partner Module Info -->
                        <div id="partner-info" class="alert alert-info" style="display: none;">
                            <strong>Módulo Parceiro:</strong> Gestão de alunos, emissão de certificados e acompanhamento do contrato.
                        </div>

                        <div class="form-group">
                            <label for="email" class="form-label">E-mail</label>
                            <div class="position-relative">
                                <i class="fas fa-envelope form-icon"></i>
                                <input 
                                    type="email" 
                                    class="form-control" 
                                    id="email" 
                                    placeholder="Digite seu e-mail"
                                    required
                                >
                            </div>
                            <div class="form-validation" id="email-error">Por favor, digite um e-mail válido.</div>
                        </div>

                        <div class="form-group">
                            <label for="password" class="form-label">Senha</label>
                            <div class="position-relative">
                                <i class="fas fa-lock form-icon"></i>
                                <input 
                                    type="password" 
                                    class="form-control" 
                                    id="password" 
                                    placeholder="Digite sua senha"
                                    required
                                >
                                <i class="fas fa-eye password-toggle" id="togglePassword"></i>
                            </div>
                            <div class="form-validation" id="password-error">A senha deve ter pelo menos 6 caracteres.</div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="rememberMe">
                                <label class="form-check-label" for="rememberMe">
                                    Lembrar de mim
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary" id="loginButton">
                            <span class="spinner"></span>
                            <span class="btn-text">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                <span id="loginButtonText">Entrar no Sistema</span>
                            </span>
                        </button>

                        <div class="forgot-password">
                            <a href="#" id="forgotPasswordLink">Esqueceu sua senha?</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // --- STATE ---
            let currentModule = 'admin';
            const users = {
                admin: { email: '<EMAIL>', password: '123456', name: 'Murilo Silva', redirect: 'administrativo.html' },
                financial: { email: '<EMAIL>', password: '123456', name: 'Maria Financeiro', redirect: 'financeiro.html' },
                partner: { email: '<EMAIL>', password: '123456', name: 'Carlos Silva - EduTech Centro', redirect: 'partner-dashboard.html' }
            };

            // --- ELEMENTS ---
            const moduleTabs = document.querySelectorAll('.module-tab');
            const loginForm = document.getElementById('loginForm');
            const emailField = document.getElementById('email');
            const passwordField = document.getElementById('password');
            const togglePassword = document.getElementById('togglePassword');
            const loginButton = document.getElementById('loginButton');
            const loginButtonText = document.getElementById('loginButtonText');
            const forgotPasswordLink = document.getElementById('forgotPasswordLink');

            // --- FUNCTIONS ---

            const showAlert = (message, type = 'danger', duration = 5000) => {
                const alertContainer = document.getElementById('globalAlert');
                const alert = document.createElement('div');
                alert.className = `alert alert-${type} alert-dismissible fade show`;
                alert.role = 'alert';
                alert.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;
                alertContainer.innerHTML = ''; // Clear previous alerts
                alertContainer.appendChild(alert);

                if (duration) {
                    setTimeout(() => alert.classList.remove('show'), duration);
                }
            };

            const setButtonLoading = (isLoading) => {
                if (isLoading) {
                    loginButton.classList.add('loading');
                    loginButton.disabled = true;
                } else {
                    loginButton.classList.remove('loading');
                    loginButton.disabled = false;
                }
            };

            const switchModule = (module) => {
                currentModule = module;
                
                moduleTabs.forEach(tab => tab.classList.toggle('active', tab.dataset.module === module));
                
                document.querySelectorAll('[id$="-info"]').forEach(info => info.style.display = 'none');
                document.getElementById(`${module}-info`).style.display = 'block';

                const userData = users[module];
                emailField.value = userData.email;
                passwordField.value = userData.password;
                
                clearFieldValidation(emailField);
                clearFieldValidation(passwordField);

                const buttonTexts = {
                    admin: 'Entrar como Administrador',
                    financial: 'Entrar como Financeiro',
                    partner: 'Entrar como Parceiro'
                };
                loginButtonText.textContent = buttonTexts[module];
            };

            const validateField = (field, validationFn, errorMsg) => {
                clearFieldValidation(field);
                if (!validationFn(field.value)) {
                    field.classList.add('is-invalid');
                    const errorElement = document.getElementById(`${field.id}-error`);
                    errorElement.textContent = errorMsg;
                    errorElement.style.display = 'block';
                    return false;
                }
                return true;
            };

            const clearFieldValidation = (field) => {
                field.classList.remove('is-invalid');
                document.getElementById(`${field.id}-error`).style.display = 'none';
            };

            const handleLogin = (e) => {
                e.preventDefault();

                const isEmailValid = validateField(emailField, (val) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), 'Por favor, digite um e-mail válido.');
                const isPasswordValid = validateField(passwordField, (val) => val.length >= 6, 'A senha deve ter pelo menos 6 caracteres.');

                if (!isEmailValid || !isPasswordValid) return;

                setButtonLoading(true);
                
                const { email, password, name, redirect } = users[currentModule];

                setTimeout(() => {
                    if (emailField.value === email && passwordField.value === password) {
                        showAlert('Login bem-sucedido! Redirecionando...', 'success', null);
                        
                        setTimeout(() => {
                            sessionStorage.setItem('userModule', currentModule);
                            sessionStorage.setItem('userName', name);
                            sessionStorage.setItem('userEmail', emailField.value);
                            window.location.href = redirect;
                        }, 1500);

                    } else {
                        showAlert('E-mail ou senha incorretos. Tente novamente.');
                        setButtonLoading(false);
                    }
                }, 1500); // Simulate API call
            };

            // --- EVENT LISTENERS ---

            moduleTabs.forEach(tab => {
                tab.addEventListener('click', () => switchModule(tab.dataset.module));
            });

            togglePassword.addEventListener('click', () => {
                const isPassword = passwordField.type === 'password';
                passwordField.type = isPassword ? 'text' : 'password';
                togglePassword.classList.toggle('fa-eye');
                togglePassword.classList.toggle('fa-eye-slash');
            });

            loginForm.addEventListener('submit', handleLogin);

            [emailField, passwordField].forEach(field => {
                field.addEventListener('input', () => clearFieldValidation(field));
            });

            forgotPasswordLink.addEventListener('click', (e) => {
                e.preventDefault();
                showAlert('Funcionalidade em desenvolvimento. Use as credenciais de teste.', 'info');
            });

            // --- INITIALIZATION ---
            switchModule('admin'); // Set initial state
            
            // Animate entrance
            document.querySelectorAll('.login-card > *').forEach((el, i) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    el.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, i * 100);
            });
        });
    </script>
</body>
</html>