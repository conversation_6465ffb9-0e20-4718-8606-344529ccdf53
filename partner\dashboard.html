<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência - Dashboard Parceiro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #0f172a;
            --secondary-color: #1e293b;
            --accent-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --light-bg: #f8fafc;
            --white-color: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --gradient-primary: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            --gradient-info: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --border-radius: 8px;
            --border-radius-lg: 12px;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-primary);
            line-height: 1.6;
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: var(--white-color);
            border-right: 1px solid var(--border-color);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .sidebar-brand {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: var(--text-primary);
        }

        .sidebar-brand-icon {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.75rem;
            color: white;
        }

        .sidebar-brand-text {
            font-size: 1.25rem;
            font-weight: 700;
        }

        .partner-info {
            background: var(--gradient-primary);
            color: white;
            padding: 1rem 1.5rem;
            margin: 1rem;
            border-radius: var(--border-radius);
            text-align: center;
        }

        .partner-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .partner-plan {
            font-size: 0.875rem;
            opacity: 0.9;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-section {
            margin-bottom: 2rem;
        }

        .nav-section-title {
            padding: 0 1.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: var(--text-muted);
            margin-bottom: 0.5rem;
        }

        .nav-item {
            margin: 0.25rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background-color: #f1f5f9;
            color: var(--accent-color);
        }

        .nav-link.active {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            font-size: 1rem;
        }

        .nav-badge {
            margin-left: auto;
            background: var(--success-color);
            color: white;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        /* Header */
        .header {
            background: var(--white-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .header-subtitle {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-menu {
            position: relative;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
        }

        /* Page Content */
        .page-content {
            padding: 2rem;
        }

        /* Contract Info Card */
        .contract-card {
            background: var(--gradient-primary);
            color: white;
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .contract-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.1); }
        }

        .contract-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .contract-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .contract-status {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            backdrop-filter: blur(10px);
        }

        .contract-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .contract-detail {
            text-align: center;
        }

        .contract-detail-label {
            font-size: 0.875rem;
            opacity: 0.9;
            margin-bottom: 0.5rem;
        }

        .contract-detail-value {
            font-size: 1.25rem;
            font-weight: 600;
        }

        /* Certificate Meter */
        .certificate-meter {
            background: var(--white-color);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        .meter-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .meter-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
        }

        .meter-title i {
            margin-right: 0.5rem;
            color: var(--accent-color);
        }

        .meter-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            align-items: center;
        }

        .progress-large {
            height: 20px;
            background: #f1f5f9;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-bar-large {
            height: 100%;
            background: var(--gradient-primary);
            border-radius: 10px;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-bar-large::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .meter-stats {
            text-align: center;
        }

        .remaining-count {
            font-size: 3rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .remaining-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .quick-action-card {
            background: var(--white-color);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            text-align: center;
            text-decoration: none;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .quick-action-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
            color: var(--text-primary);
        }

        .quick-action-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
        }

        .quick-action-title {
            font-weight: 600;
            font-size: 1.125rem;
            margin-bottom: 0.5rem;
        }

        .quick-action-desc {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        /* Cards */
        .card {
            background: var(--white-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
        }

        .card-title i {
            margin-right: 0.5rem;
            color: var(--accent-color);
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Tables */
        .table-container {
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: var(--light-bg);
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            color: var(--text-secondary);
            font-size: 0.875rem;
            border-bottom: 1px solid var(--border-color);
        }

        .table td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }

        .table tr:hover {
            background: var(--light-bg);
        }

        /* Badges */
        .badge {
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .badge.issued {
            background: #dcfce7;
            color: #166534;
        }

        .badge.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .badge.completed {
            background: #dbeafe;
            color: #1e40af;
        }

        /* Buttons */
        .btn {
            border-radius: var(--border-radius);
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--accent-color);
            color: white;
        }

        .btn-primary:hover {
            background: #7c3aed;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-outline-primary {
            background: transparent;
            color: var(--accent-color);
            border: 1px solid var(--accent-color);
        }

        .btn-outline-primary:hover {
            background: var(--accent-color);
            color: white;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        /* Student Avatar */
        .student-avatar {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
        }

        /* Activity Feed */
        .activity-item {
            display: flex;
            align-items: flex-start;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-avatar {
            width: 40px;
            height: 40px;
            background: var(--accent-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .activity-desc {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .activity-time {
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        /* Financial Summary */
        .financial-summary {
            background: var(--gradient-success);
            color: white;
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .financial-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1.5rem;
            text-align: center;
        }

        .financial-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .financial-label {
            font-size: 0.875rem;
            opacity: 0.9;
        }

        /* Certificate Preview */
        .certificate-preview {
            border: 3px solid var(--accent-color);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            margin: 1.5rem 0;
            background: linear-gradient(135deg, #f8fafc, #ffffff);
            text-align: center;
            position: relative;
            min-height: 300px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .certificate-preview::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 2px solid var(--success-color);
            border-radius: var(--border-radius);
        }

        .certificate-logo {
            width: 80px;
            height: 80px;
            background: var(--accent-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }

        .certificate-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 1rem;
        }

        .certificate-student {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 1rem 0;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--accent-color);
            display: inline-block;
        }

        .certificate-course {
            font-size: 1.125rem;
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
        }

        .certificate-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .qr-code {
            width: 60px;
            height: 60px;
            background: #f0f0f0;
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius);
        }

        /* Modal Styles */
        .modal-content {
            border: none;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xl);
        }

        .modal-header {
            background: var(--gradient-primary);
            color: white;
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
            border-bottom: none;
        }

        .modal-title {
            display: flex;
            align-items: center;
        }

        .modal-title i {
            margin-right: 0.5rem;
        }

        .btn-close {
            filter: invert(1);
        }

        /* Form Styles */
        .form-control, .form-select, .form-control:focus, .form-select:focus {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 0.75rem;
            font-size: 0.9rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
            outline: none;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .header {
                padding: 1rem;
            }

            .page-content {
                padding: 1rem;
            }

            .contract-details {
                grid-template-columns: 1fr;
            }

            .meter-content {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }

            .financial-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Dropdown Menu */
        .dropdown-menu {
            background: var(--white-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            padding: 0.5rem 0;
            min-width: 200px;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            color: var(--text-secondary);
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background: var(--light-bg);
            color: var(--text-primary);
        }

        .dropdown-item i {
            margin-right: 0.5rem;
            width: 16px;
        }

        .dropdown-divider {
            margin: 0.5rem 0;
            border-top: 1px solid var(--border-color);
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in {
            animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Loading State */
        .loading {
            position: relative;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid var(--border-color);
            border-top: 2px solid var(--accent-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* File Upload */
        .file-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
            background: var(--light-bg);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload-area:hover {
            border-color: var(--accent-color);
            background: rgba(139, 92, 246, 0.05);
        }

        .file-upload-area.dragover {
            border-color: var(--accent-color);
            background: rgba(139, 92, 246, 0.1);
        }

        /* Alert Styles */
        .alert {
            border-radius: var(--border-radius);
            border: none;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .alert-info {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
        }

        .alert-warning {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            color: #92400e;
        }

        .alert-success {
            background: linear-gradient(135deg, #d1fae5, #a7f3d0);
            color: #065f46;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="#" class="sidebar-brand">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="sidebar-brand-text">Faciência</div>
            </a>
        </div>

        <div class="partner-info">
            <div class="partner-name">EduTech Centro</div>
            <div class="partner-plan">Plano Anual - Ativo</div>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">Menu Principal</div>
                <div class="nav-item">
                    <a href="./dashboard.html" class="nav-link active">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                </div>
                <div class="nav-item">
                    <a href="./students.html" class="nav-link">
                        <i class="fas fa-users"></i>
                        Meus Alunos
                        <span class="nav-badge">12</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="./certificates.html" class="nav-link">
                        <i class="fas fa-certificate"></i>
                        Certificados
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showStudentModal()">
                        <i class="fas fa-plus-circle"></i>
                        Cadastrar Aluno
                    </a>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Financeiro</div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-credit-card"></i>
                        Meu Contrato
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-file-invoice"></i>
                        Pagamentos
                    </a>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Suporte</div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-question-circle"></i>
                        Ajuda
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-headset"></i>
                        Contato
                    </a>
                </div>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div>
                    <h1 class="header-title">Dashboard do Parceiro</h1>
                    <p class="header-subtitle">Gestão de alunos e certificados - EduTech Centro</p>
                </div>

                <div class="header-actions">
                    <div class="user-menu dropdown">
                        <div class="user-avatar" data-bs-toggle="dropdown">
                            CS
                        </div>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user"></i>Perfil</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog"></i>Configurações</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i>Sair</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <main class="page-content">
            <!-- Contract Information -->
            <div class="contract-card">
                <div class="contract-header">
                    <div>
                        <div class="contract-title">
                            <i class="fas fa-file-contract me-2"></i>
                            Contrato Ativo: #CTR-2025-001
                        </div>
                        <div class="contract-status">
                            <i class="fas fa-check-circle me-1"></i>
                            Vigente até 31/12/2025
                        </div>
                    </div>
                </div>
                
                <div class="contract-details">
                    <div class="contract-detail">
                        <div class="contract-detail-label">Tipo de Contrato</div>
                        <div class="contract-detail-value">Anual</div>
                    </div>
                    <div class="contract-detail">
                        <div class="contract-detail-label">Valor Total</div>
                        <div class="contract-detail-value">R$ 12.500,00</div>
                    </div>
                    <div class="contract-detail">
                        <div class="contract-detail-label">Dias Restantes</div>
                        <div class="contract-detail-value">174 dias</div>
                    </div>
                    <div class="contract-detail">
                        <div class="contract-detail-label">Status Financeiro</div>
                        <div class="contract-detail-value">Em Dia</div>
                    </div>
                </div>
            </div>

            <!-- Certificate Meter -->
            <div class="certificate-meter">
                <div class="meter-header">
                    <h3 class="meter-title">
                        <i class="fas fa-certificate"></i>
                        Certificados Disponíveis
                    </h3>
                </div>
                
                <div class="meter-content">
                    <div class="progress-section">
                        <div class="progress-large">
                            <div class="progress-bar-large" style="width: 65%"></div>
                        </div>
                        <div class="progress-info">
                            <span><strong>130</strong> Emitidos</span>
                            <span><strong>70</strong> Restantes</span>
                            <span><strong>200</strong> Total</span>
                        </div>
                    </div>
                    
                    <div class="meter-stats">
                        <div class="remaining-count" id="remainingCount">70</div>
                        <div class="remaining-label">Certificados<br>Disponíveis</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="#" class="quick-action-card" onclick="showStudentModal()">
                    <div class="quick-action-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="quick-action-title">Cadastrar Aluno</div>
                    <div class="quick-action-desc">Adicionar novo aluno para emissão de certificado</div>
                </a>

                <a href="#" class="quick-action-card" onclick="showCertificateModal()">
                    <div class="quick-action-icon">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <div class="quick-action-title">Emitir Certificado</div>
                    <div class="quick-action-desc">Gerar certificado para aluno cadastrado</div>
                </a>

                <a href="./students.html" class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="quick-action-title">Lista de Alunos</div>
                    <div class="quick-action-desc">Visualizar e gerenciar todos os alunos</div>
                </a>
            </div>

            <!-- Content Grid -->
            <div class="row">
                <!-- Recent Students -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-users"></i>
                                Alunos Recentes
                            </h3>
                            <a href="./students.html" class="btn btn-outline-primary btn-sm">Ver Todos</a>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Aluno</th>
                                            <th>Curso</th>
                                            <th>Data Conclusão</th>
                                            <th>Status</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="student-avatar me-3">AS</div>
                                                    <div>
                                                        <div class="fw-semibold">Ana Silva</div>
                                                        <div class="text-muted small"><EMAIL></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>Informática Básica</td>
                                            <td>08/07/2025</td>
                                            <td><span class="badge issued">Certificado Emitido</span></td>
                                            <td>
                                                <button class="btn btn-outline-primary btn-sm" onclick="viewCertificate('Ana Silva')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success btn-sm">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="student-avatar me-3" style="background: var(--gradient-success);">JS</div>
                                                    <div>
                                                        <div class="fw-semibold">João Santos</div>
                                                        <div class="text-muted small"><EMAIL></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>Excel Avançado</td>
                                            <td>09/07/2025</td>
                                            <td><span class="badge issued">Certificado Emitido</span></td>
                                            <td>
                                                <button class="btn btn-outline-primary btn-sm" onclick="viewCertificate('João Santos')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success btn-sm">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="student-avatar me-3" style="background: var(--gradient-warning);">MC</div>
                                                    <div>
                                                        <div class="fw-semibold">Maria Costa</div>
                                                        <div class="text-muted small"><EMAIL></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>Marketing Digital</td>
                                            <td>10/07/2025</td>
                                            <td><span class="badge pending">Pendente</span></td>
                                            <td>
                                                <button class="btn btn-primary btn-sm" onclick="showCertificateModal('Maria Costa')">
                                                    <i class="fas fa-certificate"></i> Emitir
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="student-avatar me-3" style="background: var(--gradient-info);">PO</div>
                                                    <div>
                                                        <div class="fw-semibold">Pedro Oliveira</div>
                                                        <div class="text-muted small"><EMAIL></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>Programação Web</td>
                                            <td>11/07/2025</td>
                                            <td><span class="badge completed">Curso Concluído</span></td>
                                            <td>
                                                <button class="btn btn-primary btn-sm" onclick="showCertificateModal('Pedro Oliveira')">
                                                    <i class="fas fa-certificate"></i> Emitir
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activity & Financial -->
                <div class="col-lg-4">
                    <!-- Financial Summary -->
                    <div class="financial-summary">
                        <h5 class="mb-3">
                            <i class="fas fa-credit-card me-2"></i>
                            Resumo Financeiro
                        </h5>
                        <div class="financial-grid">
                            <div class="financial-item">
                                <div class="financial-value">R$ 8.333</div>
                                <div class="financial-label">Pago</div>
                            </div>
                            <div class="financial-item">
                                <div class="financial-value">R$ 4.167</div>
                                <div class="financial-label">A Pagar</div>
                            </div>
                        </div>
                        <div class="alert alert-info mt-3" style="background: rgba(255,255,255,0.2); color: white; border: none;">
                            <strong>Próxima Parcela:</strong><br>
                            R$ 2.083,33 - Vence em 5 dias
                        </div>
                    </div>

                    <!-- Activity Feed -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-clock"></i>
                                Atividade Recente
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="activity-item">
                                <div class="activity-avatar">
                                    <i class="fas fa-certificate"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Certificado emitido</div>
                                    <div class="activity-desc">Ana Silva - Informática Básica</div>
                                    <div class="activity-time">2 horas atrás</div>
                                </div>
                            </div>

                            <div class="activity-item">
                                <div class="activity-avatar" style="background: var(--success-color);">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Novo aluno cadastrado</div>
                                    <div class="activity-desc">Pedro Oliveira foi adicionado</div>
                                    <div class="activity-time">1 dia atrás</div>
                                </div>
                            </div>

                            <div class="activity-item">
                                <div class="activity-avatar" style="background: var(--warning-color);">
                                    <i class="fas fa-graduation-cap"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Curso concluído</div>
                                    <div class="activity-desc">Maria Costa - Marketing Digital</div>
                                    <div class="activity-time">2 dias atrás</div>
                                </div>
                            </div>

                            <div class="activity-item">
                                <div class="activity-avatar" style="background: var(--info-color);">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Pagamento realizado</div>
                                    <div class="activity-desc">Parcela 6/12 - R$ 2.083,33</div>
                                    <div class="activity-time">3 dias atrás</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Student Registration Modal -->
    <div class="modal fade" id="studentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus"></i>
                        Cadastrar Novo Aluno
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="studentForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="studentName" class="form-label">Nome Completo *</label>
                                    <input type="text" class="form-control" id="studentName" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="studentCPF" class="form-label">CPF *</label>
                                    <input type="text" class="form-control" id="studentCPF" placeholder="000.000.000-00" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="studentBirth" class="form-label">Data de Nascimento</label>
                                    <input type="date" class="form-control" id="studentBirth">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="studentEmail" class="form-label">E-mail *</label>
                                    <input type="email" class="form-control" id="studentEmail" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="studentPhone" class="form-label">Telefone</label>
                                    <input type="text" class="form-control" id="studentPhone" placeholder="(00) 00000-0000">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="completionDate" class="form-label">Data de Conclusão *</label>
                                    <input type="date" class="form-control" id="completionDate" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="courseName" class="form-label">Curso *</label>
                                    <select class="form-select" id="courseName" required>
                                        <option value="">Selecione o curso...</option>
                                        <option value="informatica-basica">Informática Básica</option>
                                        <option value="excel-avancado">Excel Avançado</option>
                                        <option value="marketing-digital">Marketing Digital</option>
                                        <option value="design-grafico">Design Gráfico</option>
                                        <option value="programacao-web">Programação Web</option>
                                        <option value="outro">Outro (especificar)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="courseHours" class="form-label">Carga Horária</label>
                                    <input type="number" class="form-control" id="courseHours" placeholder="40">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="courseGrade" class="form-label">Nota Final</label>
                                    <input type="number" class="form-control" id="courseGrade" step="0.1" min="0" max="10" placeholder="8.5">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3" id="otherCourseField" style="display: none;">
                            <label for="otherCourseName" class="form-label">Especifique o Curso</label>
                            <input type="text" class="form-control" id="otherCourseName" placeholder="Digite o nome do curso...">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="saveStudent()">
                        <i class="fas fa-save me-2"></i>Salvar Aluno
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Certificate Generation Modal -->
    <div class="modal fade" id="certificateModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-certificate"></i>
                        Emitir Certificado
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- Form Section -->
                        <div class="col-md-5">
                            <form id="certificateForm">
                                <div class="mb-3">
                                    <label for="certificateStudent" class="form-label">Selecionar Aluno *</label>
                                    <select class="form-select" id="certificateStudent" required>
                                        <option value="">Escolha um aluno...</option>
                                        <option value="maria-costa">Maria Costa - Marketing Digital</option>
                                        <option value="pedro-oliveira">Pedro Oliveira - Programação Web</option>
                                        <option value="lucia-ferreira">Lúcia Ferreira - Design Gráfico</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="certificateTemplate" class="form-label">Modelo</label>
                                    <select class="form-select" id="certificateTemplate">
                                        <option value="padrao">Modelo Padrão Faciência</option>
                                        <option value="personalizado">Modelo Personalizado</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="certificateText" class="form-label">Texto Adicional</label>
                                    <textarea class="form-control" id="certificateText" rows="3" placeholder="Texto adicional para o certificado..."></textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Logo do Polo</label>
                                    <div class="file-upload-area" onclick="document.getElementById('partnerLogo').click()">
                                        <i class="fas fa-upload fa-2x mb-3 text-muted"></i>
                                        <p class="mb-0">Clique para fazer upload</p>
                                        <small class="text-muted">PNG, JPG (máx. 2MB)</small>
                                        <input type="file" id="partnerLogo" accept="image/*" style="display: none;">
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <strong>Certificados Restantes:</strong> <span id="certsRemaining">70</span> de 200<br>
                                    <small>Este certificado utilizará 1 da sua cota.</small>
                                </div>
                            </form>
                        </div>

                        <!-- Preview Section -->
                        <div class="col-md-7">
                            <div class="certificate-preview">
                                <div class="certificate-logo">
                                    <i class="fas fa-graduation-cap"></i>
                                </div>
                                
                                <div class="certificate-title">CERTIFICADO</div>
                                
                                <p class="mb-3">Certificamos que</p>
                                
                                <div class="certificate-student" id="previewStudentName">NOME DO ALUNO</div>
                                
                                <div class="certificate-course">
                                    concluiu com aproveitamento o curso de<br>
                                    <strong id="previewCourseName">NOME DO CURSO</strong><br>
                                    com carga horária de <span id="previewHours">40</span> horas.
                                </div>
                                
                                <div class="certificate-footer">
                                    <div>
                                        <strong>Faciência</strong><br>
                                        <small>Instituição de Ensino</small><br>
                                        <small>Data: <span id="previewDate">10/07/2025</span></small>
                                    </div>
                                    <div class="qr-code">
                                        <i class="fas fa-qrcode"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-success" onclick="saveCertificate()">
                        <i class="fas fa-check-circle me-2"></i>Emitir e Salvar Certificado
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Simulação de login
        document.addEventListener('DOMContentLoaded', function() {
            // Para teste, vamos assumir que o usuário está logado.
            sessionStorage.setItem('userModule', 'partner');
            sessionStorage.setItem('userName', 'Carlos Silva');
            sessionStorage.setItem('partnerName', 'EduTech Centro');

            const userModule = sessionStorage.getItem('userModule');
            if (!userModule || userModule !== 'partner') {
                // Em um app real, redirecionaria para a página de login
                window.location.href = '../index.html';
                console.log("Usuário não autenticado ou não é um parceiro.");
                return;
            }

            initializePage();
        });

        function initializePage() {
            // Atualiza nome do usuário no avatar
            const userName = sessionStorage.getItem('userName');
            if (userName) {
                const initials = userName.split(' ').map(n => n[0]).join('').toUpperCase();
                document.querySelector('.user-avatar').textContent = initials;
            }

            // Adiciona listeners
            addEventListeners();

            // Animações
            animateElements();
        }

        function addEventListeners() {
            // Toggle da sidebar (se houver botão para mobile)
            const sidebarToggle = document.getElementById('sidebar-toggle'); // Assumindo que um botão com este ID exista
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', () => {
                    document.getElementById('sidebar').classList.toggle('active');
                });
            }

            // Lógica para o campo "Outro curso" no modal de estudante
            const courseSelect = document.getElementById('courseName');
            if(courseSelect) {
                courseSelect.addEventListener('change', function() {
                    document.getElementById('otherCourseField').style.display = this.value === 'outro' ? 'block' : 'none';
                });
            }

            // Lógica para preview do certificado
            const studentSelectCert = document.getElementById('certificateStudent');
            if(studentSelectCert) {
                studentSelectCert.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex].text;
                    const [studentName, courseName] = selectedOption.split(' - ');
                    document.getElementById('previewStudentName').textContent = studentName || 'NOME DO ALUNO';
                    document.getElementById('previewCourseName').textContent = courseName || 'NOME DO CURSO';
                });
            }
        }

        function logout() {
            sessionStorage.clear();
            showNotification('Você foi desconectado com sucesso!', 'info');
            // Em um app real, redirecionaria para a página de login
            setTimeout(() => window.location.href = '../index.html', 2000);
        }

        // Funções dos Modais
        let studentModal, certificateModal;
        function getModals() {
            if (!studentModal) studentModal = new bootstrap.Modal(document.getElementById('studentModal'));
            if (!certificateModal) certificateModal = new bootstrap.Modal(document.getElementById('certificateModal'));
        }

        function showStudentModal() {
            getModals();
            document.getElementById('studentForm').reset();
            studentModal.show();
        }

        function showCertificateModal(studentName = '') {
            getModals();
            document.getElementById('certificateForm').reset();
            if (studentName) {
                // Simula a seleção do aluno se o nome for passado
                const select = document.getElementById('certificateStudent');
                for (let i = 0; i < select.options.length; i++) {
                    if (select.options[i].text.includes(studentName)) {
                        select.selectedIndex = i;
                        select.dispatchEvent(new Event('change'));
                        break;
                    }
                }
            }
            certificateModal.show();
        }

        function saveStudent() {
            // Lógica de validação do formulário aqui
            showNotification('Aluno cadastrado com sucesso!', 'success');
            studentModal.hide();
        }

        function saveCertificate() {
            const remainingCountEl = document.getElementById('remainingCount');
            let remaining = parseInt(remainingCountEl.textContent);
            
            if (remaining > 0) {
                remaining--;
                remainingCountEl.textContent = remaining;
                document.getElementById('certsRemaining').textContent = remaining;
                
                // Atualiza a barra de progresso
                const total = 200;
                const issued = total - remaining;
                const percentage = (issued / total) * 100;
                document.querySelector('.progress-bar-large').style.width = percentage + '%';
                document.querySelector('.progress-info span:first-child').innerHTML = `<strong>${issued}</strong> Emitidos`;
                document.querySelector('.progress-info span:nth-child(2)').innerHTML = `<strong>${remaining}</strong> Restantes`;

                showNotification('Certificado emitido com sucesso!', 'success');
                certificateModal.hide();
            } else {
                showNotification('Você não tem certificados suficientes!', 'danger');
            }
        }

        // Funções de Navegação (simuladas)
        function showStudentsList() {
            window.location.href = './students.html';
        }

        function viewCertificate(studentName) {
            showNotification(`Visualizando certificado de ${studentName}...`, 'info');
            showCertificateModal(studentName);
        }

        // Função para mostrar notificações
        function showNotification(message, type = 'success') {
            const notificationContainer = document.createElement('div');
            notificationContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
            `;
            
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show animate-fade-in`;
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            notificationContainer.appendChild(notification);
            document.body.appendChild(notificationContainer);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notificationContainer.remove(), 500);
            }, 5000);
        }

        // Função para animar elementos na entrada
        function animateElements() {
            const elements = document.querySelectorAll('.contract-card, .certificate-meter, .quick-action-card, .card');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    element.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 80);
            });
        }
    </script>
</body>
</html>
