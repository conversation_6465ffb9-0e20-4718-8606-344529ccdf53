<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência - Relatórios Financeiros</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="../css/dashboard.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="dashboard.html" class="sidebar-brand">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <span class="sidebar-brand-text">Faciência</span>
            </a>
        </div>

        <div class="partner-info" style="background: var(--gradient-success);">
            <div class="partner-name">Módulo Financeiro</div>
            <div class="partner-plan">Visão Geral</div>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">Menu Principal</div>
                <div class="nav-item">
                    <a href="dashboard.html" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="partners.html" class="nav-link">
                        <i class="fas fa-handshake"></i>
                        <span>Parceiros</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="contracts.html" class="nav-link">
                        <i class="fas fa-file-signature"></i>
                        <span>Contratos</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="invoices.html" class="nav-link">
                        <i class="fas fa-file-invoice-dollar"></i>
                        <span>Faturas</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="reports.html" class="nav-link active">
                        <i class="fas fa-chart-pie"></i>
                        <span>Relatórios</span>
                    </a>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Administração</div>
                 <div class="nav-item">
                    <a href="../administrativo.html" class="nav-link">
                        <i class="fas fa-cogs"></i>
                        <span>Painel Admin</span>
                    </a>
                </div>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div>
                    <h1 class="header-title">Relatórios Financeiros</h1>
                    <p class="header-subtitle">Análise detalhada das finanças da Faciência.</p>
                </div>
                <div class="header-actions">
                     <div class="user-menu dropdown">
                        <div class="user-avatar" data-bs-toggle="dropdown" aria-expanded="false" style="background: var(--gradient-success);">
                            FN
                        </div>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Perfil</a></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fas fa-sign-out-alt me-2"></i>Sair</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <main class="page-content">
            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Filtros de Relatório</h5>
                    <div class="d-flex align-items-center">
                        <select class="form-select me-2" style="width: 200px;">
                            <option selected>Tipo de Relatório</option>
                            <option value="1">Receita por Período</option>
                            <option value="2">Despesas por Categoria</option>
                            <option value="3">Balanço Geral</option>
                        </select>
                        <input type="date" class="form-control me-2" style="width: 150px;">
                        <input type="date" class="form-control me-2" style="width: 150px;">
                        <button class="btn btn-primary"><i class="fas fa-filter me-2"></i>Aplicar</button>
                    </div>
                </div>
            </div>

            <!-- Reports -->
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h3 class="card-title"><i class="fas fa-chart-bar me-2"></i>Receita Mensal</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="receitaMensalChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h3 class="card-title"><i class="fas fa-chart-pie me-2"></i>Despesas por Categoria</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="despesasCategoriaChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-file-alt me-2"></i>Balanço Geral Detalhado</h3>
                </div>
                <div class="card-body p-0">
                    <div class="table-container">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Categoria</th>
                                    <th>Descrição</th>
                                    <th>Data</th>
                                    <th>Valor</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-success-soft">Receita</span></td>
                                    <td>Pagamento Plano Ouro - EduTech</td>
                                    <td>10/07/2025</td>
                                    <td class="text-success fw-bold">+ R$ 1.200,00</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-danger-soft">Despesa</span></td>
                                    <td>Custo Servidor Cloud</td>
                                    <td>08/07/2025</td>
                                    <td class="text-danger fw-bold">- R$ 450,00</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success-soft">Receita</span></td>
                                    <td>Pagamento Fatura - Web Cursos</td>
                                    <td>09/07/2025</td>
                                    <td class="text-success fw-bold">+ R$ 350,00</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-danger-soft">Despesa</span></td>
                                    <td>Licença Software</td>
                                    <td>05/07/2025</td>
                                    <td class="text-danger fw-bold">- R$ 150,00</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                 <div class="card-footer text-end">
                    <button class="btn btn-secondary"><i class="fas fa-download me-2"></i>Exportar PDF</button>
                </div>
            </div>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Chart.js: Receita Mensal
            const receitaMensalCtx = document.getElementById('receitaMensalChart').getContext('2d');
            new Chart(receitaMensalCtx, {
                type: 'bar',
                data: {
                    labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul'],
                    datasets: [{
                        label: 'Receita (R$)',
                        data: [12000, 19000, 15000, 17000, 22000, 18000, 15750],
                        backgroundColor: 'rgba(40, 167, 69, 0.7)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Chart.js: Despesas por Categoria
            const despesasCategoriaCtx = document.getElementById('despesasCategoriaChart').getContext('2d');
            new Chart(despesasCategoriaCtx, {
                type: 'pie',
                data: {
                    labels: ['Servidores', 'Marketing', 'Software', 'Outros'],
                    datasets: [{
                        label: 'Despesas (R$)',
                        data: [1250, 800, 600, 800],
                        backgroundColor: [
                            'rgba(220, 53, 69, 0.7)',
                            'rgba(255, 193, 7, 0.7)',
                            'rgba(13, 202, 240, 0.7)',
                            'rgba(108, 117, 125, 0.7)'
                        ],
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true
                }
            });
        });
    </script>
</body>
</html>
