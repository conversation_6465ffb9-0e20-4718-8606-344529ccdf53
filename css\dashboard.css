* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #0f172a;
    --secondary-color: #1e293b;
    --accent-color: #8b5cf6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --light-bg: #f8fafc;
    --white-color: #ffffff;
    --text-primary: #0f172a;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --border-color: #e2e8f0;
    --gradient-primary: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --gradient-info: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --border-radius-lg: 12px;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--light-bg);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    background: var(--white-color);
    border-right: 1px solid var(--border-color);
    z-index: 1000;
    overflow-y: auto;
    transition: all 0.3s ease;
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--text-primary);
}

.sidebar-brand-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    color: white;
}

.sidebar-brand-text {
    font-size: 1.25rem;
    font-weight: 700;
}

.partner-info {
    background: var(--gradient-primary);
    color: white;
    padding: 1rem 1.5rem;
    margin: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
}

.partner-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.partner-plan {
    font-size: 0.875rem;
    opacity: 0.9;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-section {
    margin-bottom: 2rem;
}

.nav-section-title {
    padding: 0 1.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.nav-item {
    margin: 0.25rem 1rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.2s ease;
    font-weight: 500;
}

.nav-link:hover {
    background-color: #f1f5f9;
    color: var(--accent-color);
}

.nav-link.active {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.nav-link i {
    width: 20px;
    margin-right: 0.75rem;
    font-size: 1rem;
}

.nav-badge {
    margin-left: auto;
    background: var(--success-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
}

/* Main Content */
.main-content {
    margin-left: 280px;
    min-height: 100vh;
    transition: all 0.3s ease;
}

/* Header */
.header {
    background: var(--white-color);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.header-subtitle {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-menu {
    position: relative;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    cursor: pointer;
}

/* Page Content */
.page-content {
    padding: 2rem;
}

/* Contract Info Card */
.contract-card {
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.contract-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
}

.contract-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
}

.contract-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.contract-status {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    backdrop-filter: blur(10px);
}

.contract-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    position: relative;
    z-index: 2;
}

.contract-detail {
    text-align: center;
}

.contract-detail-label {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.contract-detail-value {
    font-size: 1.25rem;
    font-weight: 600;
}

/* Certificate Meter */
.certificate-meter {
    background: var(--white-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.meter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.meter-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

.meter-title i {
    margin-right: 0.5rem;
    color: var(--accent-color);
}

.meter-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    align-items: center;
}

.progress-large {
    height: 20px;
    background: #f1f5f9;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-bar-large {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 10px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar-large::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.meter-stats {
    text-align: center;
}

.remaining-count {
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.remaining-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.quick-action-card {
    background: var(--white-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    text-decoration: none;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    cursor: pointer;
}

.quick-action-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    color: var(--text-primary);
}

.quick-action-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.quick-action-title {
    font-weight: 600;
    font-size: 1.125rem;
    margin-bottom: 0.5rem;
}

.quick-action-desc {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Cards */
.card {
    background: var(--white-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

.card-title i {
    margin-right: 0.5rem;
    color: var(--accent-color);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1.5rem;
    background-color: var(--light-bg);
     border-top: 1px solid var(--border-color);
}

/* Tables */
.table-container {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th {
    background: var(--light-bg);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.875rem;
    border-bottom: 1px solid var(--border-color);
}

.table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.table tr:hover {
    background: var(--light-bg);
}

/* Badges */
.badge {
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.badge.issued, .badge.paid, .bg-success-soft {
    background-color: rgba(16, 185, 129, 0.1);
    color: #065f46;
}

.badge.pending, .bg-warning-soft {
    background-color: rgba(245, 158, 11, 0.1);
    color: #92400e;
}

.badge.completed, .bg-info-soft {
    background-color: rgba(6, 182, 212, 0.1);
    color: #0e7490;
}

.badge.overdue, .bg-danger-soft {
    background-color: rgba(239, 68, 68, 0.1);
    color: #991b1b;
}


/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background: #7c3aed;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-icon {
    width: 36px;
    height: 36px;
    padding: 0;
}

.btn-light-primary {
    background-color: rgba(139, 92, 246, 0.1);
    color: var(--accent-color);
}
.btn-light-primary:hover {
    background-color: rgba(139, 92, 246, 0.2);
}

.btn-light-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}
.btn-light-success:hover {
    background-color: rgba(16, 185, 129, 0.2);
}

.btn-light-secondary {
    background-color: #f1f5f9;
    color: var(--text-secondary);
}
.btn-light-secondary:hover {
    background-color: #e2e8f0;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-outline-primary {
    background: transparent;
    color: var(--accent-color);
    border: 1px solid var(--accent-color);
}

.btn-outline-primary:hover {
    background: var(--accent-color);
    color: white;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Student Avatar */
.student-avatar, .avatar-circle {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.avatar-circle.bg-success {
    background: var(--gradient-success);
}
.avatar-circle.bg-warning {
    background: var(--gradient-warning);
}

/* Activity Feed */
.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-avatar {
    width: 40px;
    height: 40px;
    background: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.activity-desc {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Financial Summary */
.financial-summary {
    background: var(--gradient-success);
    color: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin-bottom: 2rem;
}

.financial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
    text-align: center;
}

.financial-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.financial-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* Certificate Preview */
.certificate-preview {
    border: 3px solid var(--accent-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin: 1.5rem 0;
    background: linear-gradient(135deg, #f8fafc, #ffffff);
    text-align: center;
    position: relative;
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.certificate-preview::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border: 2px solid var(--success-color);
    border-radius: var(--border-radius);
}

.certificate-logo {
    width: 80px;
    height: 80px;
    background: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 2rem;
}

.certificate-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.certificate-student {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--accent-color);
    display: inline-block;
}

.certificate-course {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.certificate-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    font-size: 0.875rem;
    color: var(--text-muted);
}

.qr-code {
    width: 60px;
    height: 60px;
    background: #f0f0f0;
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
}

/* Modal Styles */
.modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
}

.modal-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    border-bottom: none;
}

.modal-title {
    display: flex;
    align-items: center;
}

.modal-title i {
    margin-right: 0.5rem;
}

.btn-close {
    filter: invert(1);
}

/* Form Styles */
.form-control, .form-select, .form-control:focus, .form-select:focus {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    font-size: 0.9rem;
}

.form-control:focus, .form-select:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
    outline: none;
}

.form-label {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .header {
        padding: 1rem;
    }

    .page-content {
        padding: 1rem;
    }

    .contract-details {
        grid-template-columns: 1fr;
    }

    .meter-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .quick-actions {
        grid-template-columns: 1fr;
    }

    .financial-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Dropdown Menu */
.dropdown-menu {
    background: var(--white-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem 0;
    min-width: 200px;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: var(--light-bg);
    color: var(--text-primary);
}

.dropdown-item i {
    margin-right: 0.5rem;
    width: 16px;
}

.dropdown-divider {
    margin: 0.5rem 0;
    border-top: 1px solid var(--border-color);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Loading State */
.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* File Upload */
.file-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    background: var(--light-bg);
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-upload-area:hover {
    border-color: var(--accent-color);
    background: rgba(139, 92, 246, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--accent-color);
    background: rgba(139, 92, 246, 0.1);
}

/* Alert Styles */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem;
    margin-bottom: 1rem;
}

.alert-info {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
}

.alert-warning {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    color: #92400e;
}

.alert-success {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0);
    color: #065f46;
}

.certificate-card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    background: var(--white-color);
    text-align: center;
    transition: all 0.3s ease;
}
.certificate-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}
.certificate-icon {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}
.certificate-student-name {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--text-primary);
}
.certificate-course-name {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}
.certificate-date {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 1.5rem;
}

.certificate-gallery-card {
    transition: all 0.3s ease;
}
.certificate-gallery-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}
.certificate-gallery-card .certificate-icon {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}
.certificate-gallery-card .certificate-student-name {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--text-primary);
}
.certificate-gallery-card .certificate-course-name {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}
.certificate-gallery-card .certificate-date {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 1.5rem;
}
.certificate-gallery-card.certificate-pending .certificate-icon {
    color: var(--warning-color);
}
