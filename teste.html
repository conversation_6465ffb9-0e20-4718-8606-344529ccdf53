<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema Faciência</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #27ae60;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --light-bg: #f8f9fa;
            --dark-bg: #2c3e50;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .login-card {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo-container {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .form-floating {
            margin-bottom: 1rem;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .dashboard-container {
            display: none;
            background: var(--light-bg);
            min-height: 100vh;
        }

        .navbar {
            background: white !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }

        .sidebar {
            background: var(--primary-color);
            min-height: calc(100vh - 76px);
            padding: 1.5rem 0;
        }

        .sidebar .nav-link {
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            margin: 0.2rem 1rem;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
        }

        .main-content {
            padding: 2rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
            border: none;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead {
            background: var(--primary-color);
            color: white;
        }

        .badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
        }

        .btn-sm {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            margin: 0.1rem;
        }

        .form-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .progress {
            height: 20px;
            border-radius: 10px;
            background: #e9ecef;
        }

        .progress-bar {
            border-radius: 10px;
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
        }

        .certificado-preview {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 3rem;
            text-align: center;
            background: #f8f9fa;
            margin: 1rem 0;
        }

        .hidden {
            display: none !important;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .user-profile {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .modal-content {
            border-radius: 15px;
            border: none;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            color: white;
            border-radius: 15px 15px 0 0;
        }

        .input-group-text {
            background: var(--secondary-color);
            color: white;
            border: none;
        }

        .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }

        .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn-outline-primary {
            border-color: var(--secondary-color);
            color: var(--secondary-color);
            border-radius: 10px;
        }

        .btn-outline-primary:hover {
            background: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .financial-summary {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .contract-info {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .certificates-meter {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .status-active { background: var(--accent-color); }
        .status-inactive { background: var(--danger-color); }
        .status-pending { background: var(--warning-color); }

        .quick-action-btn {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            border: none;
            border-radius: 15px;
            padding: 1.5rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 1rem;
        }

        .quick-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
            color: white;
        }

        .finance-card {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 1.5rem;
        }

        .finance-card.success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }

        .finance-card.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .partner-card {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 1.5rem;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .module-selector {
            text-align: center;
            padding: 2rem;
        }

        .module-btn {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            border: none;
            border-radius: 15px;
            padding: 2rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .module-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            color: white;
        }

        .module-btn.admin {
            background: linear-gradient(135deg, #2c3e50, #34495e);
        }

        .module-btn.financial {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }

        .module-btn.partner {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }

        .breadcrumb {
            background: none;
            padding: 0;
            margin-bottom: 2rem;
        }

        .breadcrumb-item a {
            color: var(--secondary-color);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: var(--primary-color);
        }

        .data-table {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .search-bar {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .certificate-template {
            border: 3px solid var(--secondary-color);
            border-radius: 15px;
            padding: 3rem;
            margin: 2rem 0;
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            text-align: center;
            position: relative;
        }

        .certificate-template::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 2px solid var(--accent-color);
            border-radius: 10px;
        }

        .certificate-logo {
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }

        .qr-code {
            width: 100px;
            height: 100px;
            background: #f0f0f0;
            border: 1px solid #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 1rem auto;
            border-radius: 10px;
        }

        .nav-pills .nav-link {
            border-radius: 20px;
            padding: 0.75rem 1.5rem;
            margin: 0.2rem;
            font-weight: 500;
        }

        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            border: none;
        }

        .tab-content {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-top: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .notification-badge {
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            position: absolute;
            top: -5px;
            right: -5px;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-badge.active {
            background: rgba(39, 174, 96, 0.1);
            color: var(--accent-color);
        }

        .status-badge.inactive {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger-color);
        }

        .status-badge.pending {
            background: rgba(243, 156, 18, 0.1);
            color: var(--warning-color);
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .spinner-border {
            color: var(--secondary-color);
        }

        .form-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .form-section h5 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--secondary-color);
        }

        .file-upload-area {
            border: 2px dashed var(--secondary-color);
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload-area:hover {
            background: #e9ecef;
            border-color: var(--accent-color);
        }

        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .summary-item:last-child {
            border-bottom: none;
        }

        .dropdown-menu {
            border: none;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .dropdown-item {
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            margin: 0.2rem;
        }

        .dropdown-item:hover {
            background: var(--secondary-color);
            color: white;
        }

        .alert-dismissible {
            border-radius: 10px;
            border: none;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .page-title {
            color: var(--primary-color);
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }

        .metric-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-icon {
            font-size: 3rem;
            color: var(--secondary-color);
            opacity: 0.1;
            position: absolute;
            top: 1rem;
            right: 1rem;
        }

        .position-relative {
            position: relative;
        }

        .text-gradient {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .activity-feed {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--secondary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 0.9rem;
        }

        .activity-content {
            flex: 1;
        }

        .activity-time {
            color: #6c757d;
            font-size: 0.8rem;
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .navbar-nav .nav-link {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            margin: 0.2rem;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            background: rgba(52, 152, 219, 0.1);
            color: var(--secondary-color) !important;
        }

        .footer {
            background: var(--primary-color);
            color: white;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        .footer-content {
            text-align: center;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1rem;
        }

        .social-links a {
            color: white;
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }

        .social-links a:hover {
            color: var(--secondary-color);
            transform: translateY(-2px);
        }

        .coming-soon {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .coming-soon i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: var(--secondary-color);
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 76px;
                left: -100%;
                width: 280px;
                height: calc(100vh - 76px);
                transition: left 0.3s ease;
                z-index: 1000;
            }

            .sidebar.active {
                left: 0;
            }

            .main-content {
                margin-left: 0;
            }

            .module-btn {
                font-size: 1rem;
                padding: 1.5rem;
            }

            .stats-number {
                font-size: 2rem;
            }

            .metric-value {
                font-size: 2rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-content {
            text-align: center;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--secondary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p class="mt-3">Carregando...</p>
        </div>
    </div>

    <!-- Seletor de Módulo -->
    <div id="moduleSelector" class="login-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-12 col-md-8">
                    <div class="login-card">
                        <div class="logo-container">
                            <div class="logo">
                                <i class="fas fa-graduation-cap"></i>
                                Faciência
                            </div>
                            <p class="text-muted">Sistema de Gestão de Certificados</p>
                        </div>
                        
                        <div class="module-selector">
                            <h4 class="mb-4">Selecione o Módulo de Acesso</h4>
                            <button class="module-btn admin" onclick="showModule('admin')">
                                <i class="fas fa-user-shield mb-2"></i><br>
                                Administrador
                                <small class="d-block mt-1">Gestão de Parceiros e Contratos</small>
                            </button>
                            <button class="module-btn financial" onclick="showModule('financial')">
                                <i class="fas fa-chart-line mb-2"></i><br>
                                Financeiro
                                <small class="d-block mt-1">Controle de Pagamentos</small>
                            </button>
                            <button class="module-btn partner" onclick="showModule('partner')">
                                <i class="fas fa-users mb-2"></i><br>
                                Parceiro
                                <small class="d-block mt-1">Gestão de Alunos e Certificados</small>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Login do Administrador -->
    <div id="adminLogin" class="login-container hidden">
        <div class="login-card">
            <div class="logo-container">
                <div class="logo">
                    <i class="fas fa-user-shield"></i>
                    Faciência
                </div>
                <p class="text-muted">Acesso do Administrador</p>
            </div>
            
            <form id="adminLoginForm">
                <div class="form-floating mb-3">
                    <input type="email" class="form-control" id="adminEmail" placeholder="<EMAIL>" value="<EMAIL>">
                    <label for="adminEmail"><i class="fas fa-envelope me-2"></i>E-mail</label>
                </div>
                
                <div class="form-floating mb-3">
                    <input type="password" class="form-control" id="adminPassword" placeholder="Password" value="123456">
                    <label for="adminPassword"><i class="fas fa-lock me-2"></i>Senha</label>
                </div>
                
                <button type="submit" class="btn btn-primary w-100 mb-3">
                    <i class="fas fa-sign-in-alt me-2"></i>Entrar
                </button>
                
                <div class="text-center">
                    <a href="#" class="text-decoration-none">Esqueci minha senha</a>
                </div>
            </form>
            
            <div class="text-center mt-3">
                <button class="btn btn-link" onclick="backToModuleSelector()">
                    <i class="fas fa-arrow-left me-2"></i>Voltar
                </button>
            </div>
        </div>
    </div>

    <!-- Login do Financeiro -->
    <div id="financialLogin" class="login-container hidden">
        <div class="login-card">
            <div class="logo-container">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    Faciência
                </div>
                <p class="text-muted">Acesso do Financeiro</p>
            </div>
            
            <form id="financialLoginForm">
                <div class="form-floating mb-3">
                    <input type="email" class="form-control" id="financialEmail" placeholder="<EMAIL>" value="<EMAIL>">
                    <label for="financialEmail"><i class="fas fa-envelope me-2"></i>E-mail</label>
                </div>
                
                <div class="form-floating mb-3">
                    <input type="password" class="form-control" id="financialPassword" placeholder="Password" value="123456">
                    <label for="financialPassword"><i class="fas fa-lock me-2"></i>Senha</label>
                </div>
                
                <button type="submit" class="btn btn-primary w-100 mb-3">
                    <i class="fas fa-sign-in-alt me-2"></i>Entrar
                </button>
                
                <div class="text-center">
                    <a href="#" class="text-decoration-none">Esqueci minha senha</a>
                </div>
            </form>
            
            <div class="text-center mt-3">
                <button class="btn btn-link" onclick="backToModuleSelector()">
                    <i class="fas fa-arrow-left me-2"></i>Voltar
                </button>
            </div>
        </div>
    </div>

    <!-- Login do Parceiro -->
    <div id="partnerLogin" class="login-container hidden">
        <div class="login-card">
            <div class="logo-container">
                <div class="logo">
                    <i class="fas fa-users"></i>
                    Faciência
                </div>
                <p class="text-muted">Acesso do Parceiro</p>
            </div>
            
            <form id="partnerLoginForm">
                <div class="form-floating mb-3">
                    <input type="email" class="form-control" id="partnerEmail" placeholder="<EMAIL>" value="<EMAIL>">
                    <label for="partnerEmail"><i class="fas fa-envelope me-2"></i>E-mail</label>
                </div>
                
                <div class="form-floating mb-3">
                    <input type="password" class="form-control" id="partnerPassword" placeholder="Password" value="123456">
                    <label for="partnerPassword"><i class="fas fa-lock me-2"></i>Senha</label>
                </div>
                
                <button type="submit" class="btn btn-primary w-100 mb-3">
                    <i class="fas fa-sign-in-alt me-2"></i>Entrar
                </button>
                
                <div class="text-center">
                    <a href="#" class="text-decoration-none">Esqueci minha senha</a>
                </div>
            </form>
            
            <div class="text-center mt-3">
                <button class="btn btn-link" onclick="backToModuleSelector()">
                    <i class="fas fa-arrow-left me-2"></i>Voltar
                </button>
            </div>
        </div>
    </div>

    <!-- Dashboard do Administrador -->
    <div id="adminDashboard" class="dashboard-container">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Faciência - Administrador
                </a>
                
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <div class="avatar">M</div>
                            <span class="ms-2">Murilo Silva</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Perfil</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Configurações</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>Sair</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <div class="row g-0">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2">
                <div class="sidebar">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showAdminPage('dashboard')">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showAdminPage('partners')">
                                <i class="fas fa-handshake me-2"></i>Parceiros
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showAdminPage('contracts')">
                                <i class="fas fa-file-contract me-2"></i>Contratos
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showAdminPage('reports')">
                                <i class="fas fa-chart-bar me-2"></i>Relatórios
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="main-content">
                    <!-- Dashboard Principal -->
                    <div id="adminDashboardPage" class="page-content">
                        <div class="page-header">
                            <h1 class="page-title">Dashboard Administrativo</h1>
                            <p class="page-subtitle">Visão geral do sistema e ações rápidas</p>
                        </div>

                        <!-- Métricas Principais -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="metric-card position-relative">
                                    <i class="fas fa-handshake metric-icon"></i>
                                    <div class="metric-value">24</div>
                                    <div class="metric-label">Parceiros Ativos</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card position-relative">
                                    <i class="fas fa-file-contract metric-icon"></i>
                                    <div class="metric-value">18</div>
                                    <div class="metric-label">Contratos Vigentes</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card position-relative">
                                    <i class="fas fa-certificate metric-icon"></i>
                                    <div class="metric-value">1,247</div>
                                    <div class="metric-label">Certificados Emitidos</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card position-relative">
                                    <i class="fas fa-money-bill-wave metric-icon"></i>
                                    <div class="metric-value">R$ 89,2K</div>
                                    <div class="metric-label">Receita Mensal</div>
                                </div>
                            </div>
                        </div>

                        <!-- Ações Rápidas -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Ações Rápidas</h5>
                                    </div>
                                    <div class="card-body">
                                        <button class="quick-action-btn" onclick="showAdminPage('partners', 'new')">
                                            <i class="fas fa-plus me-2"></i>Cadastrar Novo Parceiro
                                        </button>
                                        <button class="quick-action-btn" onclick="showAdminPage('contracts', 'new')">
                                            <i class="fas fa-file-plus me-2"></i>Criar Novo Contrato
                                        </button>
                                        <button class="quick-action-btn" onclick="showAdminPage('reports')">
                                            <i class="fas fa-download me-2"></i>Gerar Relatório
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="activity-feed">
                                    <h5 class="mb-3"><i class="fas fa-clock me-2"></i>Atividades Recentes</h5>
                                    <div class="activity-item">
                                        <div class="activity-icon">
                                            <i class="fas fa-plus"></i>
                                        </div>
                                        <div class="activity-content">
                                            <strong>Novo parceiro cadastrado</strong><br>
                                            <small>EduTech Centro foi adicionado ao sistema</small>
                                            <div class="activity-time">2 horas atrás</div>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <div class="activity-icon">
                                            <i class="fas fa-file-contract"></i>
                                        </div>
                                        <div class="activity-content">
                                            <strong>Contrato renovado</strong><br>
                                            <small>Instituto Saber - Contrato anual renovado</small>
                                            <div class="activity-time">5 horas atrás</div>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <div class="activity-icon">
                                            <i class="fas fa-certificate"></i>
                                        </div>
                                        <div class="activity-content">
                                            <strong>Certificados emitidos</strong><br>
                                            <small>45 certificados emitidos hoje</small>
                                            <div class="activity-time">1 dia atrás</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Contratos Próximos ao Vencimento -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Contratos Próximos ao Vencimento</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table">
                                                <thead>
                                                    <tr>
                                                        <th>Parceiro</th>
                                                        <th>Tipo de Contrato</th>
                                                        <th>Data de Vencimento</th>
                                                        <th>Certificados Restantes</th>
                                                        <th>Status</th>
                                                        <th>Ações</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><strong>Instituto Digital</strong></td>
                                                        <td>Anual</td>
                                                        <td>25/08/2025</td>
                                                        <td>45/200</td>
                                                        <td><span class="badge bg-warning">Expira em 15 dias</span></td>
                                                        <td>
                                                            <div class="action-buttons">
                                                                <button class="btn btn-sm btn-primary">Renovar</button>
                                                                <button class="btn btn-sm btn-outline-secondary">Detalhes</button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>Capacita Mais</strong></td>
                                                        <td>Semestral</td>
                                                        <td>15/08/2025</td>
                                                        <td>12/100</td>
                                                        <td><span class="badge bg-danger">Expira em 5 dias</span></td>
                                                        <td>
                                                            <div class="action-buttons">
                                                                <button class="btn btn-sm btn-primary">Renovar</button>
                                                                <button class="btn btn-sm btn-outline-secondary">Detalhes</button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Página de Parceiros -->
                    <div id="adminPartnersPage" class="page-content hidden">
                        <div class="page-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h1 class="page-title">Gestão de Parceiros</h1>
                                    <p class="page-subtitle">Cadastro e gerenciamento de polos parceiros</p>
                                </div>
                                <button class="btn btn-primary" onclick="showPartnerForm()">
                                    <i class="fas fa-plus me-2"></i>Novo Parceiro
                                </button>
                            </div>
                        </div>

                        <!-- Filtros e Busca -->
                        <div class="search-bar">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control" placeholder="Buscar parceiros...">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select">
                                        <option>Todos os Status</option>
                                        <option>Ativos</option>
                                        <option>Inativos</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select">
                                        <option>Todas as Cidades</option>
                                        <option>São Paulo</option>
                                        <option>Rio de Janeiro</option>
                                        <option>Belo Horizonte</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Tabela de Parceiros -->
                        <div class="data-table">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Parceiro</th>
                                            <th>CNPJ</th>
                                            <th>Cidade/Estado</th>
                                            <th>Responsável</th>
                                            <th>Contratos Ativos</th>
                                            <th>Status</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar me-3">ET</div>
                                                    <div>
                                                        <strong>EduTech Centro</strong><br>
                                                        <small class="text-muted"><EMAIL></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>12.345.678/0001-90</td>
                                            <td>São Paulo/SP</td>
                                            <td>Carlos Silva</td>
                                            <td>2</td>
                                            <td><span class="status-badge active">Ativo</span></td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn btn-sm btn-outline-primary" onclick="editPartner(1)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar me-3">IS</div>
                                                    <div>
                                                        <strong>Instituto Saber</strong><br>
                                                        <small class="text-muted"><EMAIL></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>98.765.432/0001-10</td>
                                            <td>Rio de Janeiro/RJ</td>
                                            <td>Ana Santos</td>
                                            <td>1</td>
                                            <td><span class="status-badge active">Ativo</span></td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn btn-sm btn-outline-primary" onclick="editPartner(2)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar me-3">CM</div>
                                                    <div>
                                                        <strong>Capacita Mais</strong><br>
                                                        <small class="text-muted"><EMAIL></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>45.678.901/0001-23</td>
                                            <td>Belo Horizonte/MG</td>
                                            <td>Roberto Lima</td>
                                            <td>1</td>
                                            <td><span class="status-badge pending">Pendente</span></td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn btn-sm btn-outline-primary" onclick="editPartner(3)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Formulário de Parceiro -->
                    <div id="partnerFormPage" class="page-content hidden">
                        <div class="page-header">
                            <h1 class="page-title">Cadastro de Parceiro</h1>
                            <p class="page-subtitle">Registre um novo polo parceiro no sistema</p>
                        </div>

                        <form id="partnerForm">
                            <!-- Informações da Empresa -->
                            <div class="form-section">
                                <h5><i class="fas fa-building me-2"></i>Informações da Empresa</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="companyName" class="form-label">Nome da Empresa *</label>
                                            <input type="text" class="form-control" id="companyName" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="cnpj" class="form-label">CNPJ *</label>
                                            <input type="text" class="form-control" id="cnpj" placeholder="00.000.000/0000-00" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="stateRegistration" class="form-label">Inscrição Estadual</label>
                                            <input type="text" class="form-control" id="stateRegistration">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Endereço -->
                            <div class="form-section">
                                <h5><i class="fas fa-map-marker-alt me-2"></i>Endereço</h5>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="cep" class="form-label">CEP *</label>
                                            <input type="text" class="form-control" id="cep" placeholder="00000-000" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="street" class="form-label">Rua *</label>
                                            <input type="text" class="form-control" id="street" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="number" class="form-label">Número *</label>
                                            <input type="text" class="form-control" id="number" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="neighborhood" class="form-label">Bairro *</label>
                                            <input type="text" class="form-control" id="neighborhood" required>
                                        </div>
                                    </div>
                                    <div class="col-md-5">
                                        <div class="mb-3">
                                            <label for="city" class="form-label">Cidade *</label>
                                            <input type="text" class="form-control" id="city" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="state" class="form-label">Estado *</label>
                                            <select class="form-select" id="state" required>
                                                <option value="">Selecione...</option>
                                                <option value="SP">São Paulo</option>
                                                <option value="RJ">Rio de Janeiro</option>
                                                <option value="MG">Minas Gerais</option>
                                                <option value="RS">Rio Grande do Sul</option>
                                                <option value="PR">Paraná</option>
                                                <option value="SC">Santa Catarina</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Informações de Contato -->
                            <div class="form-section">
                                <h5><i class="fas fa-user me-2"></i>Informações de Contato</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="responsibleName" class="form-label">Nome do Responsável *</label>
                                            <input type="text" class="form-control" id="responsibleName" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">Telefone *</label>
                                            <input type="text" class="form-control" id="phone" placeholder="(00) 00000-0000" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">E-mail *</label>
                                            <input type="email" class="form-control" id="email" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Upload de Logo -->
                            <div class="form-section">
                                <h5><i class="fas fa-image me-2"></i>Logo do Parceiro</h5>
                                <div class="file-upload-area" onclick="document.getElementById('logoFile').click()">
                                    <i class="fas fa-cloud-upload-alt fa-2x mb-3 text-muted"></i>
                                    <p class="mb-0">Clique para fazer upload da logo</p>
                                    <small class="text-muted">Formatos aceitos: JPG, PNG (máx. 2MB)</small>
                                    <input type="file" id="logoFile" accept="image/*" style="display: none;">
                                </div>
                            </div>

                            <!-- Botões de Ação -->
                            <div class="d-flex gap-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Salvar Parceiro
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="showAdminPage('partners')">
                                    <i class="fas fa-times me-2"></i>Cancelar
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Página de Contratos -->
                    <div id="adminContractsPage" class="page-content hidden">
                        <div class="page-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h1 class="page-title">Gestão de Contratos</h1>
                                    <p class="page-subtitle">Controle de contratos com parceiros</p>
                                </div>
                                <button class="btn btn-primary" onclick="showContractForm()">
                                    <i class="fas fa-plus me-2"></i>Novo Contrato
                                </button>
                            </div>
                        </div>

                        <!-- Filtros -->
                        <div class="filter-section">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">Parceiro</label>
                                    <select class="form-select">
                                        <option>Todos os Parceiros</option>
                                        <option>EduTech Centro</option>
                                        <option>Instituto Saber</option>
                                        <option>Capacita Mais</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Status</label>
                                    <select class="form-select">
                                        <option>Todos os Status</option>
                                        <option>Ativo</option>
                                        <option>Inativo</option>
                                        <option>Expirado</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Data de Início</label>
                                    <input type="date" class="form-control">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Data de Fim</label>
                                    <input type="date" class="form-control">
                                </div>
                            </div>
                        </div>

                        <!-- Tabela de Contratos -->
                        <div class="data-table">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Contrato</th>
                                            <th>Parceiro</th>
                                            <th>Tipo</th>
                                            <th>Vigência</th>
                                            <th>Certificados</th>
                                            <th>Valor</th>
                                            <th>Status</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>#CT-2024-001</strong></td>
                                            <td>EduTech Centro</td>
                                            <td>Anual</td>
                                            <td>01/01/24 - 31/12/24</td>
                                            <td><span class="badge bg-info-light">120 / 500</span></td>
                                            <td>R$ 12.000,00</td>
                                            <td><span class="badge bg-success">Ativo</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary"><i class="fas fa-pencil-alt"></i></button>
                                                <button class="btn btn-sm btn-outline-info"><i class="fas fa-eye"></i></button>
                                                <button class="btn btn-sm btn-outline-danger"><i class="fas fa-file-pdf"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>#CT-2024-002</strong></td>
                                            <td>Instituto Saber</td>
                                            <td>Mensal</td>
                                            <td>15/06/24 - 14/07/24</td>
                                            <td><span class="badge bg-info-light">30 / 100</span></td>
                                            <td>R$ 1.500,00</td>
                                            <td><span class="badge bg-success">Ativo</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary"><i class="fas fa-pencil-alt"></i></button>
                                                <button class="btn btn-sm btn-outline-info"><i class="fas fa-eye"></i></button>
                                                <button class="btn btn-sm btn-outline-danger"><i class="fas fa-file-pdf"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>#CT-2023-015</strong></td>
                                            <td>Capacita Mais</td>
                                            <td>Anual</td>
                                            <td>01/07/23 - 30/06/24</td>
                                            <td><span class="badge bg-info-light">200 / 200</span></td>
                                            <td>R$ 9.800,00</td>
                                            <td><span class="badge bg-danger">Expirado</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary"><i class="fas fa-pencil-alt"></i></button>
                                                <button class="btn btn-sm btn-outline-info"><i class="fas fa-eye"></i></button>
                                                <button class="btn btn-sm btn-outline-danger"><i class="fas fa-file-pdf"></i></button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Formulário de Contrato -->
                    <div id="adminContractFormPage" class="page-content hidden">
                        <div class="page-header">
                            <h1 class="page-title">Novo Contrato</h1>
                            <p class="page-subtitle">Preencha os dados para criar um novo contrato</p>
                        </div>
                        <form class="form-layout">
                            <div class="form-section">
                                <h5><i class="fas fa-info-circle me-2"></i>Informações do Contrato</h5>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Parceiro</label>
                                        <select class="form-select" required>
                                            <option selected disabled value="">Selecione um parceiro...</option>
                                            <option>EduTech Centro</option>
                                            <option>Instituto Saber</option>
                                            <option>Capacita Mais</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Tipo de Contrato</label>
                                        <select class="form-select" required>
                                            <option selected disabled value="">Selecione o tipo...</option>
                                            <option>Anual</option>
                                            <option>Mensal</option>
                                            <option>Por Pacote</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Data de Início</label>
                                        <input type="date" class="form-control" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Data de Fim</label>
                                        <input type="date" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            <div class="form-section">
                                <h5><i class="fas fa-dollar-sign me-2"></i>Detalhes Financeiros</h5>
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Valor Total</label>
                                        <input type="number" class="form-control" placeholder="R$ 0,00" required>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Forma de Pagamento</label>
                                        <select class="form-select" required>
                                            <option>À Vista</option>
                                            <option>Parcelado</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Nº de Parcelas</label>
                                        <input type="number" class="form-control" value="1">
                                    </div>
                                </div>
                            </div>
                             <div class="form-section">
                                <h5><i class="fas fa-certificate me-2"></i>Certificados</h5>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Quantidade Incluída</label>
                                        <input type="number" class="form-control" placeholder="0" required>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex gap-3">
                                <button type="submit" class="btn btn-primary"><i class="fas fa-save me-2"></i>Salvar Contrato</button>
                                <button type="button" class="btn btn-outline-secondary" onclick="showAdminPage('contracts')"><i class="fas fa-times me-2"></i>Cancelar</button>
                            </div>
                        </form>
                    </div>
                </div

                <!-- Conteúdo do Módulo Financeiro -->
                <div id="financialContent" class="hidden">
                    <!-- Dashboard Financeiro -->
                    <div id="financialDashboardPage" class="page-content">
                        <div class="page-header">
                            <h1 class="page-title">Dashboard Financeiro</h1>
                            <p class="page-subtitle">Visão geral das finanças</p>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="stats-card bg-success-light">
                                    <div class="stats-icon"><i class="fas fa-hand-holding-usd"></i></div>
                                    <p class="stats-title">Receita (Mês)</p>
                                    <p class="stats-number">R$ 18.500,00</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stats-card bg-warning-light">
                                    <div class="stats-icon"><i class="fas fa-hourglass-half"></i></div>
                                    <p class="stats-title">A Vencer</p>
                                    <p class="stats-number">R$ 6.500,00</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stats-card bg-danger-light">
                                    <div class="stats-icon"><i class="fas fa-exclamation-triangle"></i></div>
                                    <p class="stats-title">Em Atraso</p>
                                    <p class="stats-number">R$ 1.800,00</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Conteúdo do Módulo Parceiro -->
                <div id="partnerContent" class="hidden">
                     <div id="partnerDashboardPage" class="page-content">
                        <div class="page-header">
                            <h1 class="page-title">Painel do Parceiro</h1>
                            <p class="page-subtitle">Bem-vindo, EduTech Centro!</p>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Status do Contrato</h5>
                                        <p><strong>Plano:</strong> Anual (#CT-2024-001)</p>
                                        <p><strong>Vigência:</strong> 01/01/2024 a 31/12/2024</p>
                                        <p class="mb-2"><strong>Certificados:</strong></p>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar" style="width: 24%;" aria-valuenow="24" aria-valuemin="0" aria-valuemax="100">120 / 500</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                             <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Ações Rápidas</h5>
                                        <button class="btn btn-primary w-100 mb-2"><i class="fas fa-user-plus me-2"></i>Cadastrar Aluno</button>
                                        <button class="btn btn-secondary w-100"><i class="fas fa-certificate me-2"></i>Emitir Certificado</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Funções de Navegação e Lógica da UI
        const loginContainer = document.getElementById('loginContainer');
        const dashboardContainer = document.getElementById('dashboardContainer');
        const adminContent = document.getElementById('adminContent');
        const financialContent = document.getElementById('financialContent');
        const partnerContent = document.getElementById('partnerContent');
        const allContent = [adminContent, financialContent, partnerContent];
        const allPages = document.querySelectorAll('.page-content');

        function showDashboard(module) {
            loginContainer.style.display = 'none';
            dashboardContainer.style.display = 'flex';

            allContent.forEach(content => content.classList.add('hidden'));
            document.querySelectorAll('.sidebar .nav-link').forEach(link => link.classList.remove('active'));

            if (module === 'admin') {
                adminContent.classList.remove('hidden');
                document.querySelector('.sidebar a[href="#admin"]').classList.add('active');
                showAdminPage('dashboard');
            } else if (module === 'financial') {
                financialContent.classList.remove('hidden');
                 document.querySelector('.sidebar a[href="#financial"]').classList.add('active');
            } else if (module === 'partner') {
                partnerContent.classList.remove('hidden');
                 document.querySelector('.sidebar a[href="#partner"]').classList.add('active');
            }
        }

        function showAdminPage(pageId) {
            adminContent.querySelectorAll('.page-content').forEach(page => page.classList.add('hidden'));
            document.getElementById(`admin${pageId.charAt(0).toUpperCase() + pageId.slice(1)}Page`).classList.remove('hidden');
        }
        
        function showPartnerForm() {
            showAdminPage('partnerForm');
        }

        function showContractForm() {
            showAdminPage('contractForm');
        }

        function logout() {
            dashboardContainer.style.display = 'none';
            loginContainer.style.display = 'flex';
        }

        // Event Listeners para os botões de login
        document.getElementById('adminLoginBtn').addEventListener('click', () => showDashboard('admin'));
        document.getElementById('financialLoginBtn').addEventListener('click', () => showDashboard('financial'));
        document.getElementById('partnerLoginBtn').addEventListener('click', () => showDashboard('partner'));

    </script>
</body>
</html>