<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência - Dashboard Financeiro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="../css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="dashboard.html" class="sidebar-brand">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <span class="sidebar-brand-text">Faciência</span>
            </a>
        </div>

        <div class="partner-info" style="background: var(--gradient-success);">
            <div class="partner-name">Módulo Financeiro</div>
            <div class="partner-plan">Visão Geral</div>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">Menu Principal</div>
                <div class="nav-item">
                    <a href="dashboard.html" class="nav-link active">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="partners.html" class="nav-link">
                        <i class="fas fa-handshake"></i>
                        <span>Parceiros</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="contracts.html" class="nav-link">
                        <i class="fas fa-file-signature"></i>
                        <span>Contratos</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="invoices.html" class="nav-link">
                        <i class="fas fa-file-invoice-dollar"></i>
                        <span>Faturas</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="reports.html" class="nav-link">
                        <i class="fas fa-chart-pie"></i>
                        <span>Relatórios</span>
                    </a>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Administração</div>
                 <div class="nav-item">
                    <a href="../administrativo.html" class="nav-link">
                        <i class="fas fa-cogs"></i>
                        <span>Painel Admin</span>
                    </a>
                </div>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div>
                    <h1 class="header-title">Dashboard Financeiro</h1>
                    <p class="header-subtitle">Visão geral das finanças da Faciência.</p>
                </div>
                <div class="header-actions">
                    <div class="user-menu dropdown">
                        <div class="user-avatar" data-bs-toggle="dropdown" aria-expanded="false" style="background: var(--gradient-success);">
                            FN
                        </div>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Perfil</a></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fas fa-sign-out-alt me-2"></i>Sair</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <main class="page-content">
            <!-- Financial Summary Cards -->
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card quick-action-card h-100">
                         <div class="quick-action-icon" style="background: var(--gradient-success);">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="quick-action-title">Receita Total (Mês)</div>
                        <p class="fs-4 fw-bold">R$ 15.750,00</p>
                    </div>
                </div>
                 <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card quick-action-card h-100">
                         <div class="quick-action-icon" style="background: var(--gradient-warning);">
                            <i class="fas fa-file-invoice-dollar"></i>
                        </div>
                        <div class="quick-action-title">Pagamentos Pendentes</div>
                        <p class="fs-4 fw-bold">R$ 1.200,00</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card quick-action-card h-100">
                         <div class="quick-action-icon" style="background: var(--gradient-info);">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <div class="quick-action-title">Parceiros Ativos</div>
                        <p class="fs-4 fw-bold">42</p>
                    </div>
                </div>
                 <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card quick-action-card h-100">
                         <div class="quick-action-icon" style="background: var(--gradient-danger);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="quick-action-title">Despesas (Mês)</div>
                        <p class="fs-4 fw-bold">R$ 3.450,00</p>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exchange-alt"></i>
                        Transações Recentes
                    </h3>
                </div>
                <div class="card-body p-0">
                    <div class="table-container">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID Transação</th>
                                    <th>Parceiro</th>
                                    <th>Data</th>
                                    <th>Tipo</th>
                                    <th>Valor</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>TRN-00125</td>
                                    <td>EduTech Centro</td>
                                    <td>10/07/2025</td>
                                    <td>Renovação de Plano</td>
                                    <td class="text-success fw-bold">+ R$ 1.200,00</td>
                                    <td><span class="badge bg-success-soft">Concluído</span></td>
                                </tr>
                                <tr>
                                    <td>TRN-00124</td>
                                    <td>Web Cursos Online</td>
                                    <td>09/07/2025</td>
                                    <td>Pagamento de Fatura</td>
                                    <td class="text-success fw-bold">+ R$ 350,00</td>
                                    <td><span class="badge bg-success-soft">Concluído</span></td>
                                </tr>
                                <tr>
                                    <td>TRN-00123</td>
                                    <td>Servidor Cloud</td>
                                    <td>08/07/2025</td>
                                    <td>Despesa</td>
                                    <td class="text-danger fw-bold">- R$ 450,00</td>
                                    <td><span class="badge bg-success-soft">Pago</span></td>
                                </tr>
                                 <tr>
                                    <td>TRN-00122</td>
                                    <td>Inova Cursos</td>
                                    <td>08/07/2025</td>
                                    <td>Pagamento Pendente</td>
                                    <td class="text-warning fw-bold">R$ 200,00</td>
                                    <td><span class="badge bg-warning-soft">Pendente</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const userName = 'Financeiro';
            const initials = 'FN';
            document.querySelector('.user-avatar').textContent = initials;
        });
    </script>
</body>
</html>
