<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência - Contas a Receber</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; background: #f8fafc; color: #0f172a; }
        .sidebar { position: fixed; top: 0; left: 0; width: 280px; height: 100vh; background: #fff; border-right: 1px solid #e2e8f0; z-index: 1000; }
        .main-content { margin-left: 280px; min-height: 100vh; }
        @media (max-width: 992px) { .sidebar { left: -280px; } .sidebar.is-open { left: 0; } .main-content { margin-left: 0; } }
        .header { background: #fff; border-bottom: 1px solid #e2e8f0; padding: 1rem 2rem; position: sticky; top: 0; z-index: 100; }
        .header-title { font-size: 1.5rem; font-weight: 600; }
        .page-content { padding: 2rem; }
        .table-container { overflow-x: auto; }
        .table th, .table td { padding: 1rem; border-bottom: 1px solid #e2e8f0; }
        .badge.paid { background: #dcfce7; color: #166534; }
        .badge.overdue { background: #fee2e2; color: #991b1b; }
        .badge.due-soon { background: #fef9c3; color: #854d0e; }
        .badge.pending { background: #fef3c7; color: #92400e; }
        .filter-bar { margin-bottom: 1.5rem; display: flex; gap: 1rem; flex-wrap: wrap; }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header p-4 border-bottom">
            <a href="#" class="sidebar-brand d-flex align-items-center text-decoration-none text-dark">
                <span class="sidebar-brand-icon bg-primary text-white rounded me-2 p-2"><i class="fas fa-chart-pie"></i></span>
                <span class="sidebar-brand-text fw-bold">Faciência</span>
            </a>
        </div>
        <nav class="sidebar-nav p-3">
            <div class="nav-section mb-4">
                <span class="nav-section-title text-muted small">Menu Principal</span>
                <ul class="nav flex-column">
                    <li class="nav-item"><a href="administrativo.html" class="nav-link"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li class="nav-item"><a href="financeiro.html" class="nav-link active"><i class="fas fa-file-invoice-dollar"></i> Contas a Receber <span class="nav-badge bg-danger text-white ms-auto">3</span></a></li>
                    <li class="nav-item"><a href="transacoes.html" class="nav-link"><i class="fas fa-receipt"></i> Transações</a></li>
                    <li class="nav-item"><a href="relatorios.html" class="nav-link"><i class="fas fa-chart-bar"></i> Relatórios</a></li>
                </ul>
            </div>
            <div class="nav-section">
                <span class="nav-section-title text-muted small">Configurações</span>
                <ul class="nav flex-column">
                    <li class="nav-item"><a href="parceiros.html" class="nav-link"><i class="fas fa-users"></i> Parceiros</a></li>
                    <li class="nav-item"><a href="ajustes.html" class="nav-link"><i class="fas fa-cog"></i> Ajustes</a></li>
                </ul>
            </div>
        </nav>
    </aside>
    <!-- Main Content -->
    <main class="main-content" id="main-content">
        <header class="header d-flex align-items-center justify-content-between">
            <div>
                <h1 class="header-title">Contas a Receber</h1>
                <p class="text-muted">Visualize e gerencie os recebimentos dos contratos dos polos parceiros.</p>
            </div>
            <div class="d-flex align-items-center gap-3">
                <input type="text" class="form-control" style="width: 250px;" placeholder="Buscar por parceiro ou contrato...">
                <button class="btn btn-outline-primary"><i class="fas fa-bell"></i></button>
                <div class="user-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width:40px;height:40px;font-weight:600;">FN</div>
            </div>
        </header>
        <div class="page-content">
            <div class="filter-bar">
                <select class="form-select" style="max-width:180px;">
                    <option>Status</option>
                    <option>Pago</option>
                    <option>Pendente</option>
                    <option>Atrasado</option>
                    <option>A vencer</option>
                </select>
                <select class="form-select" style="max-width:180px;">
                    <option>Parceiro</option>
                    <option>Polo EduTech</option>
                    <option>Instituto Saber</option>
                    <option>Capacita Mais</option>
                    <option>Avançar Polos</option>
                </select>
                <button class="btn btn-primary"><i class="fas fa-filter"></i> Filtrar</button>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Parceiro</th>
                            <th>Contrato</th>
                            <th>Valor</th>
                            <th>Vencimento</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Polo EduTech</strong></td>
                            <td>#CT-2024-005</td>
                            <td>R$ 2.000,00</td>
                            <td>15/07/2025</td>
                            <td><span class="badge due-soon">A vencer</span></td>
                            <td><button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button></td>
                        </tr>
                        <tr>
                            <td><strong>Instituto Saber</strong></td>
                            <td>#CT-2024-002</td>
                            <td>R$ 1.500,00</td>
                            <td>05/07/2025</td>
                            <td><span class="badge paid">Pago</span></td>
                            <td><button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button></td>
                        </tr>
                        <tr>
                            <td><strong>Capacita Mais</strong></td>
                            <td>#CT-2024-003</td>
                            <td>R$ 1.800,00</td>
                            <td>01/06/2025</td>
                            <td><span class="badge overdue">Atrasado</span></td>
                            <td><button class="btn btn-sm btn-warning"><i class="fas fa-bell"></i></button></td>
                        </tr>
                        <tr>
                            <td><strong>Avançar Polos</strong></td>
                            <td>#CT-2024-004</td>
                            <td>R$ 2.200,00</td>
                            <td>10/07/2025</td>
                            <td><span class="badge pending">Pendente</span></td>
                            <td><button class="btn btn-sm btn-success"><i class="fas fa-check"></i></button></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </main>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function () {
                    sidebar.classList.toggle('is-open');
                });
            }
        });
    </script>
</body>
</html>