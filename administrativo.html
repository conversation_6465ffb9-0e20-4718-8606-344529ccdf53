<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência - Dashboard Administrador</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #0f172a;
            --secondary-color: #1e293b;
            --accent-color: #3b82f6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --light-bg: #f8fafc;
            --white-color: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --border-radius: 8px;
            --border-radius-lg: 12px;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-primary);
            line-height: 1.6;
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: var(--white-color);
            border-right: 1px solid var(--border-color);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .sidebar-brand {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: var(--text-primary);
        }

        .sidebar-brand-icon {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.75rem;
            color: white;
        }

        .sidebar-brand-text {
            font-size: 1.25rem;
            font-weight: 700;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-section {
            margin-bottom: 2rem;
        }

        .nav-section-title {
            padding: 0 1.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: var(--text-muted);
            margin-bottom: 0.5rem;
        }

        .nav-item {
            margin: 0.25rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background-color: #f1f5f9;
            color: var(--accent-color);
        }

        .nav-link.active {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            font-size: 1rem;
        }

        .nav-badge {
            margin-left: auto;
            background: var(--danger-color);
            color: white;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        .main-content.sidebar-closed {
            margin-left: 0;
        }

        /* Header */
        .header {
            background: var(--white-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.25rem;
            color: var(--text-secondary);
            cursor: pointer;
            display: none; /* Hidden by default on larger screens */
        }

        .header-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .header-subtitle {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-left: auto;
        }

        .search-box {
            position: relative;
            width: 300px;
        }

        .search-input {
            width: 100%;
            padding: 0.5rem 1rem 0.5rem 2.5rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--light-bg);
            font-size: 0.9rem;
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 1.25rem;
            color: var(--text-secondary);
            cursor: pointer;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--danger-color);
            color: white;
            font-size: 0.75rem;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-menu {
            position: relative;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
        }

        /* Page Content */
        .page-content {
            padding: 2rem;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stats-card {
            background: var(--white-color);
            border-radius: var(--border-radius-lg);
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--accent-color);
        }

        .stats-card.success::before { background: var(--success-color); }
        .stats-card.warning::before { background: var(--warning-color); }
        .stats-card.danger::before { background: var(--danger-color); }

        .stats-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .stats-title {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .stats-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
        }

        .stats-icon.primary { background: var(--accent-color); }
        .stats-icon.success { background: var(--success-color); }
        .stats-icon.warning { background: var(--warning-color); }
        .stats-icon.danger { background: var(--danger-color); }

        .stats-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stats-change {
            display: flex;
            align-items: center;
            font-size: 0.875rem;
        }

        .stats-change.positive {
            color: var(--success-color);
        }

        .stats-change.negative {
            color: var(--danger-color);
        }

        /* Cards */
        .card {
            background: var(--white-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
        }

        .card-title i {
            margin-right: 0.5rem;
            color: var(--accent-color);
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Buttons */
        .btn {
            border-radius: var(--border-radius);
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--accent-color);
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-outline-primary {
            background: transparent;
            color: var(--accent-color);
            border: 1px solid var(--accent-color);
        }

        .btn-outline-primary:hover {
            background: var(--accent-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        /* Tables */
        .table-container {
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: var(--light-bg);
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            color: var(--text-secondary);
            font-size: 0.875rem;
            border-bottom: 1px solid var(--border-color);
        }

        .table td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }

        .table tr:hover {
            background: var(--light-bg);
        }

        /* Badges */
        .badge {
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .badge.paid {
            background: #dcfce7;
            color: #166534;
        }

        .badge.overdue {
            background: #fee2e2;
            color: #991b1b;
        }

        .badge.due-soon {
            background: #fef9c3;
            color: #854d0e;
        }

        .badge.pending {
            background: #fef3c7;
            color: #92400e;
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .quick-action-card {
            background: var(--white-color);
            border-radius: var(--border-radius-lg);
            padding: 1.5rem;
            text-align: center;
            text-decoration: none;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .quick-action-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            color: var(--text-primary);
        }

        .quick-action-icon {
            width: 60px;
            height: 60px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .quick-action-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .quick-action-desc {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        /* Activity Feed */
        .activity-item {
            display: flex;
            align-items: flex-start;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-avatar {
            width: 40px;
            height: 40px;
            background: var(--accent-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .activity-desc {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .activity-time {
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        /* Progress Bar */
        .progress {
            background-color: var(--border-color);
            border-radius: var(--border-radius);
            height: 8px;
            overflow: hidden;
        }

        .progress-bar {
            background: var(--gradient-primary);
            height: 100%;
            border-radius: var(--border-radius);
            transition: width 0.5s ease-in-out;
        }

        .progress-bar.bg-warning {
             background: var(--gradient-warning);
        }
        .progress-bar.bg-danger {
             background: var(--gradient-danger);
        }
        .progress-bar.bg-info {
             background: var(--gradient-info);
        }

        /* Responsive */
        @media (max-width: 992px) {
            .sidebar {
                left: -280px;
            }
            .sidebar.is-open {
                left: 0;
            }
            .main-content {
                margin-left: 0;
            }
            .sidebar-toggle {
                display: block;
            }
        }
    </style>
</head>
<body>

    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="#" class="sidebar-brand">
                <span class="sidebar-brand-icon"><i class="fas fa-chart-pie"></i></span>
                <span class="sidebar-brand-text">Faciência</span>
            </a>
        </div>
        <nav class="sidebar-nav">
            <div class="nav-section">
                <span class="nav-section-title">Menu Principal</span>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a href="#" class="nav-link active">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-file-invoice-dollar"></i>
                            <span>Contas a Receber</span>
                            <span class="nav-badge">3</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-receipt"></i>
                            <span>Transações</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>Relatórios</span>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="nav-section">
                <span class="nav-section-title">Configurações</span>
                 <ul class="nav flex-column">
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span>Parceiros</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>Ajustes</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content" id="main-content">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="header-left">
                     <button class="sidebar-toggle" id="sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div>
                        <h1 class="header-title">Dashboard Financeiro</h1>
                        <p class="header-subtitle">Bem-vindo de volta, Financeiro!</p>
                    </div>
                </div>
                <div class="header-actions">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="Buscar contratos, parceiros...">
                    </div>
                     <button class="notification-btn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <div class="user-menu">
                        <div class="user-avatar">
                            <span>FN</span>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <div class="page-content">
            <!-- Financial Cards -->
            <div class="stats-grid">
                <div class="stats-card success">
                    <div class="stats-header">
                        <span class="stats-title">Receita do Mês</span>
                        <div class="stats-icon success"><i class="fas fa-dollar-sign"></i></div>
                    </div>
                    <p class="stats-value">R$ 25.840,00</p>
                    <div class="stats-change positive">
                        <i class="fas fa-arrow-up me-1"></i> 5.2% vs. mês passado
                    </div>
                </div>
                <div class="stats-card warning">
                    <div class="stats-header">
                        <span class="stats-title">Pagamentos Pendentes</span>
                        <div class="stats-icon warning"><i class="fas fa-hourglass-half"></i></div>
                    </div>
                    <p class="stats-value">R$ 6.150,00</p>
                    <div class="stats-change">
                        <span>3 Contratos</span>
                    </div>
                </div>
                <div class="stats-card danger">
                     <div class="stats-header">
                        <span class="stats-title">Pagamentos Atrasados</span>
                        <div class="stats-icon danger"><i class="fas fa-exclamation-triangle"></i></div>
                    </div>
                    <p class="stats-value">R$ 1.800,00</p>
                     <div class="stats-change negative">
                        <span>1 Contrato</span>
                    </div>
                </div>
                <div class="stats-card primary">
                     <div class="stats-header">
                        <span class="stats-title">Novos Contratos (Mês)</span>
                        <div class="stats-icon primary"><i class="fas fa-file-signature"></i></div>
                    </div>
                    <p class="stats-value">4</p>
                    <div class="stats-change">
                        <span>Totalizando R$ 8.200,00</span>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title"><i class="fas fa-receipt"></i>Últimas Contas a Receber</h5>
                            <a href="#" class="btn btn-sm btn-outline-primary">Ver Todas</a>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Parceiro</th>
                                            <th>Contrato</th>
                                            <th>Valor</th>
                                            <th>Vencimento</th>
                                            <th>Status</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Polo EduTech</strong></td>
                                            <td>#CT-2024-005</td>
                                            <td>R$ 2.000,00</td>
                                            <td>15/07/2025</td>
                                            <td><span class="badge due-soon">A vencer</span></td>
                                            <td><button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Instituto Saber</strong></td>
                                            <td>#CT-2024-002</td>
                                            <td>R$ 1.500,00</td>
                                            <td>05/07/2025</td>
                                            <td><span class="badge paid">Pago</span></td>
                                            <td><button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Capacita Mais</strong></td>
                                            <td>#CT-2024-003</td>
                                            <td>R$ 1.800,00</td>
                                            <td>01/06/2025</td>
                                            <td><span class="badge overdue">Atrasado</span></td>
                                            <td><button class="btn btn-sm btn-warning"><i class="fas fa-bell"></i></button></td>
                                        </tr>
                                         <tr>
                                            <td><strong>Avançar Polos</strong></td>
                                            <td>#CT-2024-004</td>
                                            <td>R$ 2.200,00</td>
                                            <td>10/07/2025</td>
                                            <td><span class="badge pending">Pendente</span></td>
                                            <td><button class="btn btn-sm btn-success"><i class="fas fa-check"></i></button></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title"><i class="fas fa-bullseye"></i>Metas de Receita</h5>
                        </div>
                        <div class="card-body">
                            <p>Progresso da meta mensal de R$ 30.000,00</p>
                            <div class="progress mb-2">
                                <div class="progress-bar" role="progressbar" style="width: 86%;" aria-valuenow="86" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <p class="text-end fw-bold">86%</p>

                            <p class="mt-4">Receita por tipo de contrato</p>
                            <div class="mb-2">
                                <div class="d-flex justify-content-between">
                                    <span>Anual</span>
                                    <span>R$ 15.000</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" style="width: 60%;"></div>
                                </div>
                            </div>
                             <div class="mb-2">
                                <div class="d-flex justify-content-between">
                                    <span>Mensal</span>
                                    <span>R$ 8.000</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-info" style="width: 30%;"></div>
                                </div>
                            </div>
                             <div>
                                <div class="d-flex justify-content-between">
                                    <span>Pacote</span>
                                    <span>R$ 2.840</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-warning" style="width: 10%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            const sidebarToggle = document.getElementById('sidebar-toggle');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function () {
                    sidebar.classList.toggle('is-open');
                });
            }
        });
    </script>
</body>
</html>