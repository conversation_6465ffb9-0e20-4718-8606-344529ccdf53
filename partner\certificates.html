<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência - Meus Certificados</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="../css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="dashboard.html" class="sidebar-brand">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <span class="sidebar-brand-text">Faciência</span>
            </a>
        </div>

        <div class="partner-info">
            <div class="partner-name">EduTech Centro</div>
            <div class="partner-plan">Plano Anual - Ativo</div>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">Menu Principal</div>
                <div class="nav-item">
                    <a href="dashboard.html" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="students.html" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>Meus Alunos</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="certificates.html" class="nav-link active">
                        <i class="fas fa-certificate"></i>
                        <span>Certificados</span>
                    </a>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Financeiro</div>
                <div class="nav-item">
                    <a href="contract.html" class="nav-link">
                        <i class="fas fa-file-contract"></i>
                        <span>Meu Contrato</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="payments.html" class="nav-link">
                        <i class="fas fa-dollar-sign"></i>
                        <span>Pagamentos</span>
                    </a>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Suporte</div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-question-circle"></i>
                        <span>Ajuda</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-headset"></i>
                        <span>Contato</span>
                    </a>
                </div>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div>
                    <h1 class="header-title">Certificados Emitidos</h1>
                    <p class="header-subtitle">Visualize todos os certificados gerados para seus alunos.</p>
                </div>
                <div class="header-actions">
                     <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#studentModal">
                        <i class="fas fa-plus me-2"></i>
                        Emitir Novo
                    </button>
                    <div class="user-menu dropdown">
                        <div class="user-avatar" data-bs-toggle="dropdown" aria-expanded="false">
                            CS
                        </div>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Perfil</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Configurações</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fas fa-sign-out-alt me-2"></i>Sair</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <main class="page-content">
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-filter"></i>
                        Filtros
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row g-3 align-items-center">
                        <div class="col-md-5">
                            <input type="text" class="form-control" placeholder="Buscar por aluno ou curso...">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control">
                        </div>
                        <div class="col-md-2">
                             <select class="form-select">
                                <option value="">Status</option>
                                <option value="issued">Emitido</option>
                                <option value="pending">Pendente</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100">
                                <i class="fas fa-search"></i>
                                Buscar
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Exemplo de Certificado 1 -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card certificate-gallery-card">
                        <div class="card-body text-center">
                            <div class="certificate-icon"><i class="fas fa-award"></i></div>
                            <h5 class="certificate-student-name">Ana Silva</h5>
                            <p class="certificate-course-name">Informática Básica</p>
                            <p class="certificate-date">Emitido em: 08/07/2025</p>
                            <div class="d-grid gap-2">
                                <button class="btn btn-sm btn-primary"><i class="fas fa-eye me-2"></i>Visualizar</button>
                                <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-download me-2"></i>Baixar</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Exemplo de Certificado 2 -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card certificate-gallery-card">
                        <div class="card-body text-center">
                            <div class="certificate-icon"><i class="fas fa-award"></i></div>
                            <h5 class="certificate-student-name">João Santos</h5>
                            <p class="certificate-course-name">Excel Avançado</p>
                            <p class="certificate-date">Emitido em: 09/07/2025</p>
                            <div class="d-grid gap-2">
                                <button class="btn btn-sm btn-primary"><i class="fas fa-eye me-2"></i>Visualizar</button>
                                <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-download me-2"></i>Baixar</button>
                            </div>
                        </div>
                    </div>
                </div>
                 <!-- Exemplo de Certificado 3 (Pendente) -->
                 <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card certificate-gallery-card certificate-pending">
                        <div class="card-body text-center">
                            <div class="certificate-icon"><i class="fas fa-clock"></i></div>
                            <h5 class="certificate-student-name">Maria Costa</h5>
                            <p class="certificate-course-name">Marketing Digital</p>
                            <p class="certificate-date">Conclusão: 10/07/2025</p>
                            <div class="d-grid gap-2">
                                <button class="btn btn-sm btn-success"><i class="fas fa-check me-2"></i>Emitir Agora</button>
                                <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-edit me-2"></i>Editar Aluno</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Adicione mais certificados aqui -->
            </div>
        </main>
    </div>
    
    <!-- Modal de Emissão de Certificado (reutiliza o modal de aluno) -->
    <div class="modal fade" id="studentModal" tabindex="-1" aria-labelledby="studentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="studentModalLabel">
                        <i class="fas fa-certificate me-2"></i>
                        Emitir Novo Certificado
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-3">Preencha os dados do aluno para gerar um novo certificado.</p>
                    <form id="studentForm" class="row g-3">
                        <div class="col-md-6">
                            <label for="studentName" class="form-label">Nome Completo do Aluno</label>
                            <input type="text" class="form-control" id="studentName" required>
                        </div>
                        <div class="col-md-6">
                            <label for="studentEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="studentEmail" required>
                        </div>
                        <div class="col-md-6">
                            <label for="studentCpf" class="form-label">CPF</label>
                            <input type="text" class="form-control" id="studentCpf" required>
                        </div>
                        <div class="col-md-6">
                            <label for="studentCourse" class="form-label">Curso Concluído</label>
                            <select id="studentCourse" class="form-select" required>
                                <option selected disabled value="">Selecione o curso...</option>
                                <option>Informática Básica</option>
                                <option>Excel Avançado</option>
                                <option>Marketing Digital</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="conclusionDate" class="form-label">Data de Conclusão</label>
                            <input type="date" class="form-control" id="conclusionDate" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="issueCertificate()">
                        <i class="fas fa-check me-2"></i>Emitir Certificado
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const userName = 'Carlos Silva'; // Simulado
            const initials = userName.split(' ').map(n => n[0]).join('').toUpperCase();
            document.querySelector('.user-avatar').textContent = initials;
        });

        function issueCertificate() {
            console.log('Emitindo certificado...');
            const studentModal = bootstrap.Modal.getInstance(document.getElementById('studentModal'));
            studentModal.hide();
        }
    </script>
</body>
</html>
