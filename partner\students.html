<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência - <PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="../css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="dashboard.html" class="sidebar-brand">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <span class="sidebar-brand-text">Faciência</span>
            </a>
        </div>

        <div class="partner-info">
            <div class="partner-name">EduTech Centro</div>
            <div class="partner-plan">Plano Anual - Ativo</div>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">Menu Principal</div>
                <div class="nav-item">
                    <a href="dashboard.html" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="students.html" class="nav-link active">
                        <i class="fas fa-users"></i>
                        <span>Meus Alunos</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="certificates.html" class="nav-link">
                        <i class="fas fa-certificate"></i>
                        <span>Certificados</span>
                    </a>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Financeiro</div>
                <div class="nav-item">
                    <a href="contract.html" class="nav-link">
                        <i class="fas fa-file-contract"></i>
                        <span>Meu Contrato</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="payments.html" class="nav-link">
                        <i class="fas fa-dollar-sign"></i>
                        <span>Pagamentos</span>
                    </a>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Suporte</div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-question-circle"></i>
                        <span>Ajuda</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="fas fa-headset"></i>
                        <span>Contato</span>
                    </a>
                </div>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div>
                    <h1 class="header-title">Meus Alunos</h1>
                    <p class="header-subtitle">Gerencie todos os seus alunos cadastrados.</p>
                </div>

                <div class="header-actions">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#studentModal">
                        <i class="fas fa-user-plus me-2"></i>
                        Cadastrar Aluno
                    </button>
                    <div class="user-menu dropdown">
                        <div class="user-avatar" data-bs-toggle="dropdown" aria-expanded="false">
                            CS
                        </div>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Perfil</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Configurações</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fas fa-sign-out-alt me-2"></i>Sair</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <main class="page-content">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-users"></i>
                        Lista de Alunos
                    </h3>
                    <div class="d-flex align-items-center gap-2">
                        <input type="text" class="form-control" placeholder="Buscar aluno..." style="width: 250px;">
                        <select class="form-select" style="width: 180px;">
                            <option value="">Todos os Cursos</option>
                            <option value="informatica-basica">Informática Básica</option>
                            <option value="excel-avancado">Excel Avançado</option>
                            <option value="marketing-digital">Marketing Digital</option>
                        </select>
                        <button class="btn btn-outline-secondary">
                            <i class="fas fa-filter"></i>
                            Filtrar
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-container">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Aluno</th>
                                    <th>Curso</th>
                                    <th>Data Conclusão</th>
                                    <th>Status Certificado</th>
                                    <th class="text-end">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Exemplo de Aluno 1 -->
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-3">AS</div>
                                            <div>
                                                <div class="fw-bold">Ana Silva</div>
                                                <div class="text-muted small"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>Informática Básica</td>
                                    <td>08/07/2025</td>
                                    <td><span class="badge bg-success-soft">Emitido</span></td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-icon btn-light-primary"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-sm btn-icon btn-light-success"><i class="fas fa-download"></i></button>
                                        <button class="btn btn-sm btn-icon btn-light-secondary"><i class="fas fa-edit"></i></button>
                                    </td>
                                </tr>
                                <!-- Exemplo de Aluno 2 -->
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-3 bg-success">JS</div>
                                            <div>
                                                <div class="fw-bold">João Santos</div>
                                                <div class="text-muted small"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>Excel Avançado</td>
                                    <td>09/07/2025</td>
                                    <td><span class="badge bg-success-soft">Emitido</span></td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-icon btn-light-primary"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-sm btn-icon btn-light-success"><i class="fas fa-download"></i></button>
                                        <button class="btn btn-sm btn-icon btn-light-secondary"><i class="fas fa-edit"></i></button>
                                    </td>
                                </tr>
                                <!-- Exemplo de Aluno 3 -->
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-3 bg-warning">MC</div>
                                            <div>
                                                <div class="fw-bold">Maria Costa</div>
                                                <div class="text-muted small"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>Marketing Digital</td>
                                    <td>10/07/2025</td>
                                    <td><span class="badge bg-warning-soft">Pendente</span></td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary"><i class="fas fa-certificate me-1"></i> Emitir</button>
                                        <button class="btn btn-sm btn-icon btn-light-secondary"><i class="fas fa-edit"></i></button>
                                    </td>
                                </tr>
                                <!-- Adicione mais alunos aqui -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-between align-items-center">
                    <span class="text-muted small">Mostrando 1 a 10 de 50 alunos</span>
                    <nav>
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled"><a class="page-link" href="#">Anterior</a></li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item"><a class="page-link" href="#">Próximo</a></li>
                        </ul>
                    </nav>
                </div>
            </div>
        </main>
    </div>

    <!-- Student Registration Modal -->
    <div class="modal fade" id="studentModal" tabindex="-1" aria-labelledby="studentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="studentModalLabel">
                        <i class="fas fa-user-plus me-2"></i>
                        Cadastrar Novo Aluno
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="studentForm" class="row g-3">
                        <div class="col-md-6">
                            <label for="studentName" class="form-label">Nome Completo</label>
                            <input type="text" class="form-control" id="studentName" required>
                        </div>
                        <div class="col-md-6">
                            <label for="studentEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="studentEmail" required>
                        </div>
                        <div class="col-md-6">
                            <label for="studentCpf" class="form-label">CPF</label>
                            <input type="text" class="form-control" id="studentCpf" required>
                        </div>
                        <div class="col-md-6">
                            <label for="studentCourse" class="form-label">Curso</label>
                            <select id="studentCourse" class="form-select" required>
                                <option selected disabled value="">Selecione...</option>
                                <option>Informática Básica</option>
                                <option>Excel Avançado</option>
                                <option>Marketing Digital</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="conclusionDate" class="form-label">Data de Conclusão</label>
                            <input type="date" class="form-control" id="conclusionDate" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="saveStudent()">
                        <i class="fas fa-save me-2"></i>Salvar Aluno
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Funções JS
        document.addEventListener('DOMContentLoaded', function() {
            const userName = 'Carlos Silva'; // Simulado
            const initials = userName.split(' ').map(n => n[0]).join('').toUpperCase();
            document.querySelector('.user-avatar').textContent = initials;
        });

        function saveStudent() {
            // Lógica para salvar
            console.log('Salvando aluno...');
            const studentModal = bootstrap.Modal.getInstance(document.getElementById('studentModal'));
            studentModal.hide();
        }
    </script>
</body>
</html>
